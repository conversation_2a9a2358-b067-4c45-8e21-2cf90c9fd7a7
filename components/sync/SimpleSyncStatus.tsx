'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SimpleSyncTest } from '@/lib/services/simple-sync-test';
import { simpleSync } from '@/lib/services/simple-sync';
import { Wifi, WifiOff, Activity, Database, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export function SimpleSyncStatus() {
  const [syncStatus, setSyncStatus] = useState<any[]>([]);
  const [syncHealth, setSyncHealth] = useState<any>(null);
  const [isTestingSync, setIsTestingSync] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  // Update sync status every 5 seconds
  useEffect(() => {
    const updateStatus = () => {
      const status = SimpleSyncTest.getDetailedStatus();
      const health = SimpleSyncTest.getSyncHealth();
      setSyncStatus(status.connections);
      setSyncHealth(health);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const handleTestSync = async () => {
    setIsTestingSync(true);
    setTestResult(null);
    
    try {
      const result = await SimpleSyncTest.testSync();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        message: `Test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsTestingSync(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'connecting':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'connecting':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="space-y-4">
      {/* Sync Health Overview */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            {syncHealth?.isHealthy ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            Sync Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {syncHealth?.activeConnections || 0}
              </div>
              <div className="text-sm text-gray-600">Active Syncs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {syncHealth?.totalDocsTransferred || 0}
              </div>
              <div className="text-sm text-gray-600">Docs Synced</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {syncHealth?.totalConnections || 0}
              </div>
              <div className="text-sm text-gray-600">Total Connections</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600">Last Sync</div>
              <div className="text-sm font-medium">
                {syncHealth?.lastSyncTime 
                  ? new Date(syncHealth.lastSyncTime).toLocaleTimeString()
                  : 'Never'
                }
              </div>
            </div>
          </div>

          {/* Health Issues */}
          {syncHealth?.issues && syncHealth.issues.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-red-600 mb-2">Issues:</h4>
              <ul className="text-sm text-red-600 space-y-1">
                {syncHealth.issues.map((issue: string, index: number) => (
                  <li key={index} className="flex items-center gap-2">
                    <XCircle className="h-3 w-3" />
                    {issue}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Test Sync Button */}
          <div className="flex gap-2">
            <Button 
              onClick={handleTestSync} 
              disabled={isTestingSync}
              size="sm"
              variant="outline"
            >
              {isTestingSync ? (
                <>
                  <Activity className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Test Sync
                </>
              )}
            </Button>
          </div>

          {/* Test Result */}
          {testResult && (
            <div className={`mt-3 p-3 rounded-md ${
              testResult.success 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <div className={`text-sm font-medium ${
                testResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {testResult.success ? '✅' : '❌'} {testResult.message}
              </div>
              {testResult.details && (
                <div className="text-xs text-gray-600 mt-1">
                  {JSON.stringify(testResult.details, null, 2)}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Individual Sync Connections */}
      {syncStatus.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle>Sync Connections</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {syncStatus.map((connection, index) => (
                <div key={connection.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(connection.status)}
                    <div>
                      <div className="font-medium">{connection.id}</div>
                      <div className="text-sm text-gray-600">
                        DB: {connection.dbName}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(connection.status)}>
                      {connection.status}
                    </Badge>
                    {connection.docsTransferred > 0 && (
                      <Badge variant="outline">
                        {connection.docsTransferred} docs
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}