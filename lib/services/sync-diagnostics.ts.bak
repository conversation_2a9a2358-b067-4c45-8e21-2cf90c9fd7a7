/**
 * Sync Diagnostics - Debug database instance issues
 * 
 * This helps diagnose why the database instance doesn't have PouchDB methods
 */

export class SyncDiagnostics {
  
  /**
   * Comprehensive database instance diagnosis
   */
  static async diagnoseDatabaseInstance(): Promise<{
    success: boolean;
    details: any;
    recommendations: string[];
  }> {
    const details: any = {};
    const recommendations: string[] = [];
    
    try {
      console.log('[SyncDiagnostics] 🔍 Starting comprehensive database diagnosis...');
      
      // 1. Check if we're in the right environment
      details.environment = {
        isBrowser: typeof window !== 'undefined',
        isElectron: typeof window !== 'undefined' && Boolean((window as any).electronAPI),
        isMobile: typeof window !== 'undefined' && Boolean((window as any).Capacitor),
        hasIndexedDB: typeof window !== 'undefined' && Bo<PERSON>an(window.indexedDB),
        hasLocalStorage: typeof window !== 'undefined' && Boolean(window.localStorage)
      };
      
      // 2. Check auth data
      let restaurantId = null;
      try {
        const authData = localStorage.getItem('auth_data');
        if (authData) {
          const parsedAuth = JSON.parse(authData);
          restaurantId = parsedAuth.restaurantId;
          details.auth = {
            hasAuthData: true,
            hasRestaurantId: Boolean(restaurantId),
            restaurantId: restaurantId
          };
        } else {
          details.auth = { hasAuthData: false };
          recommendations.push('No auth data found - user may not be logged in');
        }
      } catch (error) {
        details.auth = { error: error instanceof Error ? error.message : String(error) };
        recommendations.push('Auth data is corrupted or invalid');
      }
      
      // 3. Check database name generation
      if (restaurantId) {
        try {
          const { getRestaurantDbName } = await import('@/lib/db/db-utils');
          const dbName = getRestaurantDbName(restaurantId);
          details.databaseName = {
            restaurantId,
            generatedDbName: dbName,
            isValid: Boolean(dbName && dbName.startsWith('resto-'))
          };
          
          if (!dbName.startsWith('resto-')) {
            recommendations.push('Database name does not follow expected format (resto-*)');
          }
        } catch (error) {
          details.databaseName = { error: error instanceof Error ? error.message : String(error) };
          recommendations.push('Failed to generate database name');
        }
      }
      
      // 4. Check PouchDB availability
      try {
        const { getPouchDB } = await import('@/lib/db/pouchdb-instance');
        const PouchDB = await getPouchDB();
        
        details.pouchDB = {
          isAvailable: Boolean(PouchDB),
          constructorType: PouchDB ? typeof PouchDB : 'undefined',
          constructorName: PouchDB ? PouchDB.name : 'N/A'
        };
        
        if (!PouchDB) {
          recommendations.push('PouchDB constructor is not available - library may not be loaded');
        } else {
          // Test creating a temporary PouchDB instance
          try {
            const testDB = new PouchDB('test-db-diagnostic');
            details.pouchDB.canCreateInstance = true;
            details.pouchDB.testInstanceMethods = {
              hasSync: typeof testDB.sync === 'function',
              hasPut: typeof testDB.put === 'function',
              hasGet: typeof testDB.get === 'function',
              hasRemove: typeof testDB.remove === 'function'
            };
            
            // Clean up test instance
            try {
              await testDB.destroy();
            } catch (cleanupError) {
              console.warn('[SyncDiagnostics] Failed to cleanup test DB:', cleanupError);
            }
            
          } catch (createError) {
            details.pouchDB.canCreateInstance = false;
            details.pouchDB.createError = createError instanceof Error ? createError.message : String(createError);
            recommendations.push('Cannot create PouchDB instances - check PouchDB initialization');
          }
        }
      } catch (error) {
        details.pouchDB = { error: error instanceof Error ? error.message : String(error) };
        recommendations.push('Failed to import PouchDB - module may not be available');
      }
      
      // 5. Check database wrapper
      try {
        const { getMainDbInstance } = await import('@/lib/db/pouchdb-instance');
        const dbWrapper = await getMainDbInstance();
        
        details.databaseWrapper = {
          isAvailable: Boolean(dbWrapper),
          wrapperType: dbWrapper ? typeof dbWrapper : 'undefined',
          wrapperConstructor: dbWrapper ? dbWrapper.constructor?.name : 'N/A'
        };
        
        if (dbWrapper) {
          // Check wrapper methods
          details.databaseWrapper.methods = {
            hasGetDatabase: typeof dbWrapper.getDatabase === 'function',
            hasInitialize: typeof dbWrapper.initialize === 'function',
            hasCleanup: typeof dbWrapper.cleanup === 'function'
          };
          
          // Try to get database from wrapper
          try {
            const db = dbWrapper.getDatabase();
            details.databaseWrapper.wrappedDatabase = {
              isAvailable: Boolean(db),
              dbType: db ? typeof db : 'undefined',
              dbConstructor: db ? db.constructor?.name : 'N/A'
            };
            
            if (db) {
              details.databaseWrapper.wrappedDatabase.methods = {
                hasSync: typeof db.sync === 'function',
                hasPut: typeof db.put === 'function',
                hasGet: typeof db.get === 'function',
                hasRemove: typeof db.remove === 'function'
              };
              
              if (typeof db.sync !== 'function') {
                recommendations.push('Database from wrapper is missing sync method - may not be a PouchDB instance');
              }
              if (typeof db.put !== 'function') {
                recommendations.push('Database from wrapper is missing put method - may not be a PouchDB instance');
              }
            } else {
              recommendations.push('Database wrapper returned null/undefined database');
            }
          } catch (getDbError) {
            details.databaseWrapper.getDbError = getDbError instanceof Error ? getDbError.message : String(getDbError);
            recommendations.push('Failed to get database from wrapper');
          }
        } else {
          recommendations.push('Database wrapper is not available - may not be initialized');
        }
      } catch (error) {
        details.databaseWrapper = { error: error instanceof Error ? error.message : String(error) };
        recommendations.push('Failed to import database wrapper');
      }
      
      // 6. Overall assessment
      const hasWorkingPouchDB = details.pouchDB?.isAvailable && details.pouchDB?.canCreateInstance;
      const hasWorkingWrapper = details.databaseWrapper?.isAvailable && details.databaseWrapper?.wrappedDatabase?.isAvailable;
      const hasValidAuth = details.auth?.hasAuthData && details.auth?.hasRestaurantId;
      
      const success = hasWorkingPouchDB && (hasWorkingWrapper || hasValidAuth);
      
      if (!success) {
        if (!hasWorkingPouchDB) {
          recommendations.push('CRITICAL: PouchDB is not working - check PouchDB initialization');
        }
        if (!hasValidAuth) {
          recommendations.push('CRITICAL: Authentication data is missing - user needs to log in');
        }
        if (!hasWorkingWrapper) {
          recommendations.push('WARNING: Database wrapper is not working - may need direct PouchDB approach');
        }
      }
      
      console.log('[SyncDiagnostics] ✅ Diagnosis complete');
      console.log('[SyncDiagnostics] 📊 Details:', details);
      console.log('[SyncDiagnostics] 💡 Recommendations:', recommendations);
      
      return {
        success,
        details,
        recommendations
      };
      
    } catch (error) {
      console.error('[SyncDiagnostics] ❌ Diagnosis failed:', error);
      return {
        success: false,
        details: { error: error instanceof Error ? error.message : String(error) },
        recommendations: ['Diagnosis failed - check console for errors']
      };
    }
  }
  
  /**
   * Quick database connectivity test
   */
  static async testDatabaseConnectivity(): Promise<{
    success: boolean;
    method: 'wrapper' | 'direct' | 'failed';
    dbName?: string;
    error?: string;
  }> {
    try {
      // Get restaurant ID
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        return { success: false, method: 'failed', error: 'No auth data' };
      }
      
      const parsedAuth = JSON.parse(authData);
      const restaurantId = parsedAuth.restaurantId;
      if (!restaurantId) {
        return { success: false, method: 'failed', error: 'No restaurant ID' };
      }
      
      const { getRestaurantDbName } = await import('@/lib/db/db-utils');
      const dbName = getRestaurantDbName(restaurantId);
      
      // Method 1: Try wrapper approach
      try {
        const { getMainDbInstance } = await import('@/lib/db/pouchdb-instance');
        const dbWrapper = await getMainDbInstance();
        
        if (dbWrapper) {
          const db = dbWrapper.getDatabase();
          if (db && typeof db.put === 'function') {
            return { success: true, method: 'wrapper', dbName };
          }
        }
      } catch (wrapperError) {
        console.warn('[SyncDiagnostics] Wrapper method failed:', wrapperError);
      }
      
      // Method 2: Try direct PouchDB approach
      try {
        const { getPouchDB } = await import('@/lib/db/pouchdb-instance');
        const PouchDB = await getPouchDB();
        
        if (PouchDB) {
          const db = new PouchDB(dbName);
          if (typeof db.put === 'function') {
            return { success: true, method: 'direct', dbName };
          }
        }
      } catch (directError) {
        console.warn('[SyncDiagnostics] Direct method failed:', directError);
      }
      
      return { success: false, method: 'failed', error: 'Both wrapper and direct methods failed' };
      
    } catch (error) {
      return { 
        success: false, 
        method: 'failed', 
        error: error instanceof Error ? error.message : String(error) 
      };
    }
  }
}