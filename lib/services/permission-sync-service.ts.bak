/**
 * Permission Sync Service
 * 
 * Handles syncing permissions for staff on fresh devices
 * Integrates with P2P sync and provides graceful fallbacks
 */

import { toast } from 'sonner';

export interface PermissionSyncResult {
  success: boolean;
  message: string;
  hasPermissions?: boolean;
  error?: string;
}

export class PermissionSyncService {
  private static instance: PermissionSyncService;
  
  public static getInstance(): PermissionSyncService {
    if (!PermissionSyncService.instance) {
      PermissionSyncService.instance = new PermissionSyncService();
    }
    return PermissionSyncService.instance;
  }

  /**
   * Check if user has any permissions
   */
  public checkUserPermissions(user: any): boolean {
    if (!user || user.role === 'owner' || user.role === 'admin') {
      return true; // Owners and admins always have permissions
    }

    if (user.role === 'staff' && user.permissions?.pages) {
      return Object.values(user.permissions.pages).some(Boolean);
    }

    return false;
  }

  /**
   * Attempt to trigger P2P LAN sync
   */
  public async triggerLanSync(): Promise<PermissionSyncResult> {
    try {
      console.log('🌐 PERMISSION SYNC - Attempting P2P LAN sync');
      
      // Check if LAN sync is available (Electron environment)
      if (typeof window !== 'undefined' && (window as any).lanSync?.forceSyncNow) {
        await (window as any).lanSync.forceSyncNow();
        
        return {
          success: true,
          message: 'LAN sync triggered successfully. Please wait for sync to complete.'
        };
      } else {
        return {
          success: false,
          message: 'LAN sync not available in this environment.',
          error: 'LAN_SYNC_UNAVAILABLE'
        };
      }
    } catch (error) {
      console.error('❌ PERMISSION SYNC - LAN sync error:', error);
      return {
        success: false,
        message: 'Failed to trigger LAN sync.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Refresh authentication to check for new permissions
   */
  public async refreshPermissions(): Promise<PermissionSyncResult> {
    try {
      console.log('🔄 PERMISSION SYNC - Refreshing permissions from PouchDB');
      
      // Get current user from localStorage
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        return {
          success: false,
          message: 'No authentication data found.',
          error: 'NO_AUTH_DATA'
        };
      }

      const { userId } = JSON.parse(authData);
      if (!userId) {
        return {
          success: false,
          message: 'No user ID found in auth data.',
          error: 'NO_USER_ID'
        };
      }

      // Import PouchDB functions dynamically to avoid SSR issues
      const { getAllStaff } = await import('@/lib/db/v4');
      
      try {
        // Get all staff and find the one with matching userId
        const allStaff = await getAllStaff();
        const staffMember = allStaff.find(staff => staff.userId === userId);
        
        if (staffMember?.permissions?.pages) {
          const hasPermissions = Object.values(staffMember.permissions.pages).some(Boolean);
          
          if (hasPermissions) {
            // Permissions found! Reload to apply them
            setTimeout(() => {
              window.location.reload();
            }, 1000);
            
            return {
              success: true,
              message: 'Permissions found in PouchDB! Reloading application...',
              hasPermissions: true
            };
          }
        }
        
        return {
          success: false,
          message: 'No permissions found yet in PouchDB. Try syncing with the main terminal.',
          hasPermissions: false
        };
      } catch (pouchError) {
        console.error('❌ PERMISSION SYNC - PouchDB error:', pouchError);
        return {
          success: false,
          message: 'Error accessing local database. Please try again.',
          error: 'POUCHDB_ERROR'
        };
      }
    } catch (error) {
      console.error('❌ PERMISSION SYNC - Error refreshing permissions:', error);
      return {
        success: false,
        message: 'Error checking permissions. Please try again.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Complete sync workflow for fresh devices
   */
  public async performFullSync(): Promise<PermissionSyncResult> {
    try {
      console.log('🔄 PERMISSION SYNC - Starting full sync workflow');
      
      // Step 1: Try LAN sync
      const lanResult = await this.triggerLanSync();
      if (lanResult.success) {
        toast.success('🌐 LAN sync triggered. Checking for permissions...');
        
        // Wait a moment for sync to process
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Step 2: Check for permissions
        const permissionResult = await this.refreshPermissions();
        if (permissionResult.hasPermissions) {
          return permissionResult;
        }
      }

      // If LAN sync failed or no permissions found, suggest manual methods
      return {
        success: false,
        message: 'Automatic sync failed. Please try the QR code method or ask your manager for help.',
        error: 'SYNC_INCOMPLETE'
      };
    } catch (error) {
      console.error('❌ PERMISSION SYNC - Full sync error:', error);
      return {
        success: false,
        message: 'Sync process failed. Please try manual methods.',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Handle QR code sync completion
   */
  public async handleQrSyncComplete(): Promise<PermissionSyncResult> {
    console.log('✅ PERMISSION SYNC - QR sync completed, checking permissions');
    
    // Wait a moment for data to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check for permissions
    return this.refreshPermissions();
  }

  /**
   * Get user-friendly instructions for fresh device setup
   */
  public getFreshDeviceInstructions(): string[] {
    return [
      '📱 This device needs to sync with the main terminal to get your permissions.',
      '🌐 Make sure you\'re connected to the same WiFi network as the main terminal.',
      '🔄 Try the automatic network sync first - it\'s the easiest method.',
      '📷 If automatic sync doesn\'t work, use the QR code method.',
      '👥 Ask your manager to show you the sync QR code on the main terminal.',
      '⚡ Once synced, you\'ll have access to all your assigned features.'
    ];
  }

  /**
   * Check if device appears to be fresh (no permissions data)
   */
  public isFreshDevice(user: any): boolean {
    if (!user || user.role === 'owner' || user.role === 'admin') {
      return false; // Owners/admins are never "fresh"
    }

    if (user.role === 'staff') {
      // Check if staff has no permissions or all permissions are false
      if (!user.permissions?.pages) {
        return true;
      }
      
      const hasAnyPermission = Object.values(user.permissions.pages).some(Boolean);
      return !hasAnyPermission;
    }

    return false;
  }
}

// Export singleton instance
export const permissionSyncService = PermissionSyncService.getInstance(); 