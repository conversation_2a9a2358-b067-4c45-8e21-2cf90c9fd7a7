// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

import { CapacitorHttp } from '@capacitor/core';
import { isMobileApp } from '@/lib/utils/environment';
import { generateDeviceId } from '@/lib/db/db-utils';

export interface DiscoveredDevice {
  ip: string;
  port: number;
  hostname: string;
  responseTime: number;
  serverInfo?: any;
  isVerified: boolean;
  lastSeen: Date;
  deviceId?: string; // For self-sync prevention
  isSelf?: boolean; // Flag to identify self
}

export interface SimpleDiscoveryResult {
  devices: DiscoveredDevice[];
  scanDuration: number;
}

export interface DiscoveryStats {
  totalAttempts: number;
  successfulConnections: number;
  averageResponseTime: number;
  activeDevices: number;
  cachedDevices: number;
  lastDiscoveryTime?: Date;
}

export interface NetworkDiagnostics {
  localIP?: string;
  subnet?: string;
  scannedIPs: number;
  responsiveIPs: number;
  averageResponseTime: number;
  lastScanTime?: Date;
  errors: string[];
}

export interface UnifiedDiscoveryResult {
  localDevices: DiscoveredDevice[];
  networkConditions: NetworkDiagnostics;
  duration: number;
}

export class SimpleIPDiscovery {
  private readonly PRIMARY_SUBNET = '192.168.1';
  private readonly FALLBACK_SUBNETS = ['192.168.0', '10.0.0', '172.16.0'];
  private readonly PORTS = [5984, 5985, 5986, 5987]; // 5984 is most common CouchDB port
  private readonly TIMEOUT = 500; // Ultra-fast 0.5s timeout
  private readonly MAX_CONCURRENT = 50; // Massive concurrency
  private isScanning = false;
  private cachedDevices: DiscoveredDevice[] = [];
  private stats: DiscoveryStats = {
    totalAttempts: 0,
    successfulConnections: 0,
    averageResponseTime: 0,
    activeDevices: 0,
    cachedDevices: 0
  };
  private networkDiagnostics: NetworkDiagnostics = {
    scannedIPs: 0,
    responsiveIPs: 0,
    averageResponseTime: 0,
    errors: []
  };
  private responseTimes: number[] = [];
  private readonly CACHE_KEY = 'ip_discovery_cache';
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes cache
  private myDeviceId: string;

  constructor() {
    this.myDeviceId = generateDeviceId();
    this.loadCachedDevices();
    console.log(`🔧 [SimpleIPDiscovery] My device ID: ${this.myDeviceId}`);
  }

  private loadCachedDevices(): void {
    try {
      if (typeof window === 'undefined') return;
      
      const cached = localStorage.getItem(this.CACHE_KEY);
      if (cached) {
        const { devices, timestamp } = JSON.parse(cached);
        const now = Date.now();
        
        if (now - timestamp < this.CACHE_DURATION) {
          this.cachedDevices = devices.map((device: any) => ({
            ...device,
            lastSeen: new Date(device.lastSeen)
          }));
          console.log(`[SimpleIPDiscovery] 📦 Loaded ${this.cachedDevices.length} cached devices`);
        } else {
          console.log('[SimpleIPDiscovery] 🧹 Cache expired, will perform fresh scan');
          localStorage.removeItem(this.CACHE_KEY);
        }
      }
    } catch (error) {
      console.warn('[SimpleIPDiscovery] Failed to load cache:', error);
    }
  }

  private saveCachedDevices(): void {
    try {
      if (typeof window === 'undefined') return;
      
      const cacheData = {
        devices: this.cachedDevices,
        timestamp: Date.now()
      };
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cacheData));
      console.log(`[SimpleIPDiscovery] 💾 Saved ${this.cachedDevices.length} devices to cache`);
    } catch (error) {
      console.warn('[SimpleIPDiscovery] Failed to save cache:', error);
    }
  }

  async findDevices(): Promise<SimpleDiscoveryResult> {
    const startTime = Date.now();
    const devices: DiscoveredDevice[] = [];

    if (this.isScanning) {
      console.log('[SimpleIPDiscovery] Already scanning, returning cached results');
      return {
        devices: [...this.cachedDevices],
        scanDuration: Date.now() - startTime
      };
    }

    // Quick cache check first - if we have valid cached CouchDB servers, return them
    // but exclude self devices
    if (this.cachedDevices.length > 0) {
      const cachedCouchDB = this.cachedDevices.filter(device => 
        (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
        !device.isSelf // Exclude self devices
      );
      
      if (cachedCouchDB.length > 0) {
        console.log(`[SimpleIPDiscovery] 📦 Using cached CouchDB servers: ${cachedCouchDB.length} found (excluding self)`);
        return {
          devices: [...this.cachedDevices],
          scanDuration: Date.now() - startTime
        };
      }
    }

    this.isScanning = true;
    this.stats.totalAttempts = 0;
    this.stats.successfulConnections = 0;
    this.responseTimes = [];
    this.networkDiagnostics.scannedIPs = 0;
    this.networkDiagnostics.responsiveIPs = 0;
    this.networkDiagnostics.errors = [];

    console.log('[SimpleIPDiscovery] 🔍 Starting ULTRA-FAST IP discovery scan...');

    try {
      // Phase 1: Scan primary subnet (192.168.1.x) first
      console.log('[SimpleIPDiscovery] 🚀 Phase 1: Scanning primary subnet 192.168.1.x');
      const primaryDevices = await this.scanSubnet(this.PRIMARY_SUBNET);
      devices.push(...primaryDevices);
      
      // Check if we found any CouchDB servers in primary subnet (excluding self)
      const couchDBFound = primaryDevices.some(device => 
        (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
        !device.isSelf
      );
      
      if (couchDBFound) {
        console.log(`[SimpleIPDiscovery] ✅ Found CouchDB servers in primary subnet, skipping fallback subnets`);
      } else {
        console.log('[SimpleIPDiscovery] ⚠️ No CouchDB in primary subnet, scanning fallback subnets');
        // Phase 2: Only scan fallback subnets if no CouchDB found in primary
        for (const subnet of this.FALLBACK_SUBNETS) {
          const fallbackDevices = await this.scanSubnet(subnet);
          devices.push(...fallbackDevices);
          
          // Check if we found CouchDB in this fallback subnet (excluding self)
          const fallbackCouchDBFound = fallbackDevices.some(device => 
            (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
            !device.isSelf
          );
          
          if (fallbackCouchDBFound) {
            console.log(`[SimpleIPDiscovery] ✅ Found CouchDB servers in ${subnet}.x, stopping scan`);
            break; // Stop scanning other subnets
          }
        }
      }

      this.cachedDevices = devices;
      this.stats.lastDiscoveryTime = new Date();
      this.stats.successfulConnections = devices.length;
      this.stats.activeDevices = devices.length;
      this.stats.cachedDevices = this.cachedDevices.length;
      this.stats.averageResponseTime = this.responseTimes.length > 0 
        ? this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length 
        : 0;
        
      this.networkDiagnostics.averageResponseTime = this.stats.averageResponseTime;
      this.networkDiagnostics.lastScanTime = new Date();
      this.networkDiagnostics.responsiveIPs = devices.length;

      // Save to cache
      this.saveCachedDevices();

      console.log(`[SimpleIPDiscovery] ✅ Discovery complete: ${devices.length} devices found in ${Date.now() - startTime}ms`);
      
      return {
        devices,
        scanDuration: Date.now() - startTime
      };
    } finally {
      this.isScanning = false;
    }
  }

  async startDiscovery(): Promise<UnifiedDiscoveryResult> {
    const startTime = Date.now();
    const result = await this.findDevices();
    
    return {
      localDevices: result.devices,
      networkConditions: this.networkDiagnostics,
      duration: result.scanDuration
    };
  }

  private async scanSubnet(subnet: string): Promise<DiscoveredDevice[]> {
    const devices: DiscoveredDevice[] = [];
    const allPromises: Promise<void>[] = [];

    console.log(`[SimpleIPDiscovery] 🚀 ULTRA-FAST scanning subnet ${subnet}.0/24 with ${this.MAX_CONCURRENT} concurrent connections`);
    
    // Create all IP scanning promises
    for (let i = 1; i <= 254; i++) {
      const ip = `${subnet}.${i}`;
      allPromises.push(this.checkIP(ip, devices));
    }

    // Process in batches to control concurrency
    for (let i = 0; i < allPromises.length; i += this.MAX_CONCURRENT) {
      const batch = allPromises.slice(i, i + this.MAX_CONCURRENT);
      await Promise.allSettled(batch);
      
      // Early termination: if we found CouchDB servers (excluding self), stop scanning
      const couchDBFound = devices.some(device => 
        (device.serverInfo?.couchdb || device.port >= 5984 && device.port <= 5987) &&
        !device.isSelf
      );
      
      if (couchDBFound) {
        console.log(`[SimpleIPDiscovery] ⚡ Found CouchDB servers in ${subnet}.x, stopping subnet scan early`);
        break;
      }
    }

    console.log(`[SimpleIPDiscovery] ✅ Subnet ${subnet}.0/24 scan complete: ${devices.length} devices found`);
    return devices;
  }

  private async checkIP(ip: string, devices: DiscoveredDevice[]): Promise<void> {
    this.stats.totalAttempts++;
    this.networkDiagnostics.scannedIPs++;
    
    // Port prioritization: try 5984 first (most common CouchDB port)
    const prioritizedPorts = [5984, ...this.PORTS.filter(p => p !== 5984)];
    
    for (const port of prioritizedPorts) {
      try {
        const url = `http://${ip}:${port}/`;
        const startTime = Date.now();
        
        const response = isMobileApp() 
          ? await CapacitorHttp.get({ url, connectTimeout: this.TIMEOUT })
          : await fetch(url, { 
              signal: AbortSignal.timeout(this.TIMEOUT),
              mode: 'cors'
            });

        const responseTime = Date.now() - startTime;
        this.responseTimes.push(responseTime);

        if ((isMobileApp() && response.status === 200) || (!isMobileApp() && response.ok)) {
          console.log(`[SimpleIPDiscovery] ⚡ Found HTTP server at ${ip}:${port} (${responseTime}ms)`);
          
          // Try to get server info
          let serverInfo: any = null;
          let isVerified = false;
          let deviceId: string | undefined;
          let isSelf = false;
          
          try {
            const infoResponse = isMobileApp() ? response : await response.json();
            const data = isMobileApp() ? infoResponse.data : infoResponse;
            serverInfo = data;
            isVerified = !!(data?.couchdb || data?.version || data?.uuid);
            
            // Check for device ID in server info to prevent self-sync
            deviceId = data?.deviceId || data?.device_id;
            if (deviceId && deviceId === this.myDeviceId) {
              isSelf = true;
              console.log(`[SimpleIPDiscovery] 🚫 Self-sync prevention: Detected own device at ${ip}:${port}`);
            }
            
            if (data?.couchdb) {
              console.log(`[SimpleIPDiscovery] 🎯 CouchDB server found at ${ip}:${port}! ${isSelf ? '(SELF - will be excluded)' : ''}`);
            }
          } catch (error) {
            // Still consider it found even if we can't parse the info
            isVerified = true;
          }

          // Check if this might be localhost/self based on IP
          if (ip === '127.0.0.1' || ip === 'localhost') {
            isSelf = true;
            console.log(`[SimpleIPDiscovery] 🚫 Self-sync prevention: Detected localhost at ${ip}:${port}`);
          }

          const device: DiscoveredDevice = {
            ip,
            port,
            hostname: ip,
            responseTime,
            serverInfo,
            isVerified,
            lastSeen: new Date(),
            deviceId,
            isSelf
          };
          
          devices.push(device);
          console.log(`[SimpleIPDiscovery] ✅ Device added: ${ip}:${port} (CouchDB: ${!!serverInfo?.couchdb}, Self: ${isSelf})`);
          break; // Found working port, no need to check others
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        if (!errorMsg.includes('AbortError') && !errorMsg.includes('timeout')) {
          this.networkDiagnostics.errors.push(`${ip}:${port} - ${errorMsg}`);
        }
      }
    }
  }

  stopDiscovery(): void {
    this.isScanning = false;
    console.log('[SimpleIPDiscovery] ⏹️ Discovery stopped');
  }

  getStats(): DiscoveryStats {
    return { ...this.stats };
  }

  getNetworkDiagnostics(): NetworkDiagnostics {
    return { ...this.networkDiagnostics };
  }

  getCachedDevices(): DiscoveredDevice[] {
    return [...this.cachedDevices];
  }

  clearCache(): void {
    this.cachedDevices = [];
    this.stats.cachedDevices = 0;
    
    // Clear localStorage cache too
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.CACHE_KEY);
    }
    
    console.log('[SimpleIPDiscovery] 🧹 Cache cleared (memory and localStorage)');
  }

  async testCleartextHttp(): Promise<boolean> {
    try {
      const testUrl = 'http://httpbin.org/get';
      console.log('[SimpleIPDiscovery] 🧪 Testing cleartext HTTP capability...');
      
      const response = isMobileApp()
        ? await CapacitorHttp.get({ url: testUrl, connectTimeout: 5000 })
        : await fetch(testUrl, { signal: AbortSignal.timeout(5000) });
      
      const success = (isMobileApp() && response.status === 200) || (!isMobileApp() && response.ok);
      console.log(`[SimpleIPDiscovery] ${success ? '✅' : '❌'} Cleartext HTTP test ${success ? 'passed' : 'failed'}`);
      return success;
    } catch (error) {
      console.log('[SimpleIPDiscovery] ❌ Cleartext HTTP test failed:', error);
      return false;
    }
  }

  getIsScanning(): boolean {
    return this.isScanning;
  }

  // Additional utility methods for the standalone IP discovery
  async quickScan(subnet: string = '192.168.1'): Promise<DiscoveredDevice[]> {
    console.log(`[SimpleIPDiscovery] 🚀 Quick scan of ${subnet}.0/24`);
    return this.scanSubnet(subnet);
  }

  async testSpecificIP(ip: string, port: number = 5984): Promise<DiscoveredDevice | null> {
    console.log(`[SimpleIPDiscovery] 🎯 Testing specific IP ${ip}:${port}`);
    const devices: DiscoveredDevice[] = [];
    
    try {
      const url = `http://${ip}:${port}/`;
      const startTime = Date.now();
      
      const response = isMobileApp() 
        ? await CapacitorHttp.get({ url, connectTimeout: this.TIMEOUT })
        : await fetch(url, { 
            signal: AbortSignal.timeout(this.TIMEOUT),
            mode: 'cors'
          });

      const responseTime = Date.now() - startTime;

      if ((isMobileApp() && response.status === 200) || (!isMobileApp() && response.ok)) {
        let serverInfo: any = null;
        let isVerified = false;
        
        try {
          const infoResponse = isMobileApp() ? response : await response.json();
          const data = isMobileApp() ? infoResponse.data : infoResponse;
          serverInfo = data;
          isVerified = !!(data?.couchdb || data?.version || data?.uuid);
        } catch (error) {
          console.log(`[SimpleIPDiscovery] Could not parse server info from ${ip}:${port}:`, error);
          isVerified = true;
        }

        // Check for self-sync prevention
        let deviceId: string | undefined;
        let isSelf = false;
        
        try {
          deviceId = serverInfo?.deviceId || serverInfo?.device_id;
          if (deviceId && deviceId === this.myDeviceId) {
            isSelf = true;
          }
        } catch (error) {
          // Ignore error
        }
        
        if (ip === '127.0.0.1' || ip === 'localhost') {
          isSelf = true;
        }

        const device: DiscoveredDevice = {
          ip,
          port,
          hostname: ip,
          responseTime,
          serverInfo,
          isVerified,
          lastSeen: new Date(),
          deviceId,
          isSelf
        };
        
        console.log(`[SimpleIPDiscovery] ✅ Found device at ${ip}:${port} (${responseTime}ms, Self: ${isSelf})`);
        return device;
      }
    } catch (error) {
      console.log(`[SimpleIPDiscovery] ❌ Failed to connect to ${ip}:${port}:`, error);
    }
    
    return null;
  }

  // Health check for cached devices
  async validateCachedDevices(): Promise<DiscoveredDevice[]> {
    if (this.cachedDevices.length === 0) return [];
    
    console.log(`[SimpleIPDiscovery] 🔍 Validating ${this.cachedDevices.length} cached devices`);
    const validDevices: DiscoveredDevice[] = [];
    
    const healthCheckPromises = this.cachedDevices.map(async (device) => {
      try {
        const url = `http://${device.ip}:${device.port}/`;
        const response = isMobileApp() 
          ? await CapacitorHttp.get({ url, connectTimeout: 1000 }) // Quick health check
          : await fetch(url, { 
              signal: AbortSignal.timeout(1000),
              mode: 'cors'
            });
        
        if ((isMobileApp() && response.status === 200) || (!isMobileApp() && response.ok)) {
          validDevices.push({
            ...device,
            lastSeen: new Date()
          });
          console.log(`[SimpleIPDiscovery] ✅ Cached device ${device.ip}:${device.port} is still online (Self: ${device.isSelf || false})`);
        } else {
          console.log(`[SimpleIPDiscovery] ❌ Cached device ${device.ip}:${device.port} is offline`);
        }
      } catch (error) {
        console.log(`[SimpleIPDiscovery] ❌ Cached device ${device.ip}:${device.port} failed health check`);
      }
    });
    
    await Promise.allSettled(healthCheckPromises);
    
    if (validDevices.length !== this.cachedDevices.length) {
      console.log(`[SimpleIPDiscovery] 🔄 Cache validation: ${validDevices.length}/${this.cachedDevices.length} devices still online`);
      this.cachedDevices = validDevices;
      this.saveCachedDevices();
    }
    
    return validDevices;
  }
}