// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

import { useState, useEffect, useCallback, useRef } from 'react';
import { SimpleSync, SimpleSyncConnection } from '@/lib/services/simple-sync';

export interface UseAutonomousSyncConfig {
  enabled?: boolean;
}

export interface DiscoveredServer {
  ip: string;
  port: number;
  serverInfo?: any;
  responseTime: number;
  isVerified: boolean;
  lastSeen: Date;
  isSelf?: boolean;
}

export interface DiscoveryState {
  isScanning: boolean;
  devicesFound: number;
  couchDBServers: number;
  lastScan?: Date;
  errors: string[];
  discoveredServers: DiscoveredServer[];
}

export interface SyncError {
  type: 'database_not_ready' | 'no_restaurant_id' | 'discovery_failed' | 'sync_error';
  message: string;
  timestamp: Date;
}

export interface AutonomousSyncState {
  isActive: boolean;
  connections: SimpleSyncConnection[];
  stats: any;
  discovery: DiscoveryState;
  errors: SyncError[];
  initStatus: 'pending' | 'ready' | 'failed';
}

export function useAutonomousSync(dbInstance: any, config: UseAutonomousSyncConfig = {}) {
  const [syncState, setSyncState] = useState<AutonomousSyncState>({
    isActive: false,
    connections: [],
    stats: { totalConnections: 0, activeConnections: 0, errorConnections: 0, totalDocsTransferred: 0 },
    discovery: {
      isScanning: false,
      devicesFound: 0,
      couchDBServers: 0,
      errors: [],
      discoveredServers: []
    },
    errors: [],
    initStatus: 'pending'
  });

  const simpleSyncRef = useRef<SimpleSync | null>(null);

  // Expose SimpleSync ref to window for debug interface access
  useEffect(() => {
    (window as any).__simpleSyncRef = simpleSyncRef;
    return () => {
      delete (window as any).__simpleSyncRef;
    };
  }, []);

  const addError = useCallback((type: SyncError['type'], message: string) => {
    setSyncState(prev => ({
      ...prev,
      errors: [...prev.errors, { type, message, timestamp: new Date() }]
    }));
  }, []);

  const checkInitialization = useCallback(async () => {
    try {
      // Check if database is ready
      if (!dbInstance) {
        console.warn('[useAutonomousSync] Database instance not available');
        setSyncState(prev => ({ ...prev, initStatus: 'failed' }));
        addError('database_not_ready', 'Database instance not available');
        return false;
      }

      // Check if database is initialized
      if (!dbInstance.isInitialized) {
        console.warn('[useAutonomousSync] Database not initialized yet');
        setSyncState(prev => ({ ...prev, initStatus: 'failed' }));
        addError('database_not_ready', 'Database not initialized yet');
        return false;
      }

      // Check if restaurant ID exists
      const restaurantId = dbInstance.getCurrentRestaurantId?.();
      if (!restaurantId) {
        console.warn('[useAutonomousSync] No restaurant ID configured');
        setSyncState(prev => ({ ...prev, initStatus: 'failed' }));
        addError('no_restaurant_id', 'No restaurant ID configured');
        return false;
      }

      console.log(`[useAutonomousSync] ✅ Initialization check passed: ${restaurantId}`);
      setSyncState(prev => ({ ...prev, initStatus: 'ready' }));
      return true;
    } catch (error) {
      console.error('[useAutonomousSync] Initialization check failed:', error);
      setSyncState(prev => ({ ...prev, initStatus: 'failed' }));
      addError('database_not_ready', `Initialization check failed: ${error instanceof Error ? error.message : String(error)}`);
      return false;
    }
  }, [dbInstance, addError]);

  const startSync = useCallback(async () => {
    if (!config.enabled || syncState.isActive) return;
    
    console.log('🚀 [useAutonomousSync] Starting sync process...');
    
    // Check initialization first
    const canStart = await checkInitialization();
    if (!canStart) {
      console.error('❌ [useAutonomousSync] Cannot start sync - initialization failed');
      return;
    }
    
    try {
      if (!simpleSyncRef.current) {
        simpleSyncRef.current = new SimpleSync();
        
        // Listen to discovery events
        simpleSyncRef.current.on('discovery-started', () => {
          setSyncState(prev => ({
            ...prev,
            discovery: { ...prev.discovery, isScanning: true }
          }));
        });

        simpleSyncRef.current.on('discovery-completed', (data: any) => {
          // Get detailed discovery state from SimpleSync
          const discoveryState = simpleSyncRef.current?.getDiscoveryState();
          const cachedDevices = discoveryState?.cachedDevices || [];
          
          // Convert cached devices to discovered servers format
          const discoveredServers: DiscoveredServer[] = cachedDevices.map((device: any) => ({
            ip: device.ip,
            port: device.port,
            serverInfo: device.serverInfo,
            responseTime: device.responseTime,
            isVerified: device.isVerified,
            lastSeen: new Date(device.lastSeen),
            isSelf: device.isSelf
          }));

          setSyncState(prev => ({
            ...prev,
            discovery: {
              ...prev.discovery,
              isScanning: false,
              devicesFound: data.totalDevices || 0,
              couchDBServers: data.couchDBServers || 0,
              lastScan: new Date(),
              discoveredServers
            }
          }));
        });

        simpleSyncRef.current.on('discovery-error', (error: any) => {
          setSyncState(prev => ({
            ...prev,
            discovery: {
              ...prev.discovery,
              isScanning: false,
              errors: [...prev.discovery.errors, error.message || String(error)]
            }
          }));
          addError('discovery_failed', error.message || String(error));
        });
        
        // Listen to enhanced sync events
        simpleSyncRef.current.on('sync-started', (data: any) => {
          console.log(`🔄 [useAutonomousSync] Sync started: ${data.connectionId}`);
          updateState();
        });
        
        simpleSyncRef.current.on('sync-progress', (data: any) => {
          if (data.changeInfo && data.changeInfo.docs_written > 0) {
            console.log(`📄 [useAutonomousSync] Sync progress: ${data.docsTransferred} docs transferred`);
          }
          updateState();
        });
        
        simpleSyncRef.current.on('sync-error', (data: any) => {
          const errorMsg = data.error?.message || 'Sync error occurred';
          console.error(`❌ [useAutonomousSync] Sync error: ${errorMsg}`);
          addError('sync_error', errorMsg);
          updateState();
        });

        simpleSyncRef.current.on('sync-complete', (data: any) => {
          console.log(`✅ [useAutonomousSync] Sync completed: ${data.connectionId}`);
          updateState();
        });

        simpleSyncRef.current.on('sync-denied', (data: any) => {
          console.warn(`🚫 [useAutonomousSync] Sync denied: ${data.connectionId}`);
          addError('sync_error', `Sync denied for ${data.connectionId}`);
          updateState();
        });

        simpleSyncRef.current.on('sync-paused', (data: any) => {
          console.log(`⏸️ [useAutonomousSync] Sync paused: ${data.connectionId}`);
          updateState();
        });

        simpleSyncRef.current.on('sync-active', (data: any) => {
          console.log(`▶️ [useAutonomousSync] Sync active: ${data.connectionId}`);
          updateState();
        });
      }
      
      console.log('🔄 [useAutonomousSync] Starting SimpleSync...');
      await simpleSyncRef.current.start();
      updateState();
      console.log('✅ [useAutonomousSync] Sync started successfully');
      
    } catch (error) {
      console.error('❌ [useAutonomousSync] Failed to start sync:', error);
      addError('sync_error', `Failed to start sync: ${error instanceof Error ? error.message : String(error)}`);
    }
  }, [config.enabled, syncState.isActive, checkInitialization, addError]);

  const stopSync = useCallback(async () => {
    if (!syncState.isActive || !simpleSyncRef.current) return;
    
    try {
      await simpleSyncRef.current.stop();
      updateState();
    } catch (error) {
      console.error('Failed to stop sync:', error);
    }
  }, [syncState.isActive]);

  const updateState = useCallback(() => {
    if (!simpleSyncRef.current) return;
    
    setSyncState(prev => ({
      ...prev,
      isActive: simpleSyncRef.current!.isRunning,
      connections: simpleSyncRef.current!.getConnections(),
      stats: simpleSyncRef.current!.getStats()
    }));
  }, []);

  useEffect(() => {
    if (config.enabled) {
      console.log('[useAutonomousSync] Effect: Config enabled, checking initialization...');
      checkInitialization().then(canStart => {
        if (canStart) {
          console.log('[useAutonomousSync] Effect: Starting sync...');
          startSync();
        } else {
          console.log('[useAutonomousSync] Effect: Cannot start sync, initialization check failed');
        }
      });
    } else {
      console.log('[useAutonomousSync] Effect: Config disabled, stopping sync...');
      stopSync();
    }

    return () => {
      if (simpleSyncRef.current) {
        simpleSyncRef.current.stop();
      }
    };
  }, [config.enabled, checkInitialization, startSync, stopSync]);

  return {
    syncState,
    startSync,
    stopSync,
    isActive: syncState.isActive,
    connections: syncState.connections,
    stats: syncState.stats,
    discovery: syncState.discovery,
    errors: syncState.errors,
    initStatus: syncState.initStatus
  };
}