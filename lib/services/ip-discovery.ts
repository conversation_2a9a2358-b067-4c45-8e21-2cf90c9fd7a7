interface DiscoveredServer {
  ip: string;
  port: number;
  version?: string;
  url: string;
}

interface DiscoveryOptions {
  timeout?: number;
  maxConcurrent?: number;
}

const DEFAULT_PORTS = [5984, 5985, 5986, 5987];
const PRIMARY_SUBNET = '192.168.1';
const FALLBACK_SUBNETS = ['192.168.0', '10.0.0', '172.16.0'];

async function testCouchDBServer(ip: string, port: number, timeout = 3000): Promise<DiscoveredServer | null> {
  const url = `http://${ip}:${port}`;
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(url, {
      method: 'GET',
      signal: controller.signal,
      headers: { 'Accept': 'application/json' }
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) return null;
    
    const data = await response.json();
    
    if (data.couchdb && data.version) {
      return {
        ip,
        port,
        version: data.version,
        url
      };
    }
    
    return null;
  } catch (error) {
    return null;
  }
}

async function scanSubnet(subnet: string, ports: number[], options: DiscoveryOptions): Promise<DiscoveredServer[]> {
  const { timeout = 3000, maxConcurrent = 10 } = options;
  const servers: DiscoveredServer[] = [];
  
  for (let i = 1; i <= 254; i += maxConcurrent) {
    const batch = [];
    
    for (let j = 0; j < maxConcurrent && i + j <= 254; j++) {
      const ip = `${subnet}.${i + j}`;
      
      for (const port of ports) {
        batch.push(testCouchDBServer(ip, port, timeout));
      }
    }
    
    const results = await Promise.allSettled(batch);
    
    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        servers.push(result.value);
      }
    });
    
    if (servers.length > 0 && subnet === PRIMARY_SUBNET) {
      return servers;
    }
  }
  
  return servers;
}

export async function discoverCouchDBServers(options: DiscoveryOptions = {}): Promise<DiscoveredServer[]> {
  console.log('🔍 Starting CouchDB server discovery...');
  
  let servers = await scanSubnet(PRIMARY_SUBNET, [5984], options);
  
  if (servers.length > 0) {
    console.log(`✅ Found ${servers.length} server(s) on primary subnet:`, servers);
    return servers;
  }
  
  console.log('🔄 Primary subnet scan complete, trying fallback ports...');
  servers = await scanSubnet(PRIMARY_SUBNET, DEFAULT_PORTS.slice(1), options);
  
  if (servers.length > 0) {
    console.log(`✅ Found ${servers.length} server(s) with fallback ports:`, servers);
    return servers;
  }
  
  console.log('🔄 Expanding to fallback subnets...');
  for (const subnet of FALLBACK_SUBNETS) {
    servers = await scanSubnet(subnet, DEFAULT_PORTS, options);
    
    if (servers.length > 0) {
      console.log(`✅ Found ${servers.length} server(s) on ${subnet}:`, servers);
      return servers;
    }
  }
  
  console.log('❌ No CouchDB servers discovered');
  return [];
}