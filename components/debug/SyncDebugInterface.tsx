'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Search, 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Server, 
  Database, 
  Clock,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';
import { useSync } from '@/lib/hooks/use-sync';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';

export function SyncDebugInterface() {
  const {
    servers,
    discovering,
    connected,
    syncing,
    status,
    currentServer,
    discover,
    connect,
    disconnect,
    error
  } = useSync();

  const [autoRefresh, setAutoRefresh] = useState(true);
  const [dbStatus, setDbStatus] = useState({
    initialized: false,
    restaurantId: null as string | null,
    dbName: null as string | null
  });

  useEffect(() => {
    const updateDbStatus = () => {
      setDbStatus({
        initialized: mainDbInstance.isInitialized,
        restaurantId: mainDbInstance.getCurrentRestaurantId(),
        dbName: mainDbInstance.getCurrentRestaurantId() 
          ? `resto-${mainDbInstance.getCurrentRestaurantId()?.replace(/^restaurant[-_:]?/, '')}`
          : null
      });
    };

    updateDbStatus();

    if (autoRefresh) {
      const interval = setInterval(updateDbStatus, 2000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const handleAutoConnect = async () => {
    if (servers.length > 0 && !connected) {
      await connect(servers[0]);
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (date?: Date) => {
    if (!date) return 'Never';
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error: {error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RotateCcw className="h-5 w-5" />
            <span>Sync Control Panel</span>
          </CardTitle>
          <CardDescription>
            Manage server discovery and sync connections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={discover} 
              disabled={discovering}
              variant="outline"
              size="sm"
            >
              <Search className="mr-2 h-4 w-4" />
              {discovering ? 'Discovering...' : 'Discover Servers'}
            </Button>
            
            {servers.length > 0 && !connected && (
              <Button 
                onClick={handleAutoConnect}
                size="sm"
              >
                <Play className="mr-2 h-4 w-4" />
                Connect to First Server
              </Button>
            )}

            {connected && (
              <Button 
                onClick={disconnect}
                variant="destructive"
                size="sm"
              >
                <Pause className="mr-2 h-4 w-4" />
                Disconnect
              </Button>
            )}

            <Button
              onClick={() => setAutoRefresh(!autoRefresh)}
              variant={autoRefresh ? "default" : "outline"}
              size="sm"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Auto Refresh: {autoRefresh ? 'ON' : 'OFF'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Database Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-5 w-5" />
            <span>Database Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Initialization</div>
              <Badge variant={dbStatus.initialized ? "default" : "secondary"}>
                {dbStatus.initialized ? (
                  <>
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Ready
                  </>
                ) : (
                  <>
                    <AlertCircle className="mr-1 h-3 w-3" />
                    Not Ready
                  </>
                )}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Restaurant ID</div>
              <div className="text-xs font-mono bg-gray-100 p-1 rounded">
                {dbStatus.restaurantId || 'None'}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Database Name</div>
              <div className="text-xs font-mono bg-gray-100 p-1 rounded">
                {dbStatus.dbName || 'None'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Server Discovery */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>Discovered Servers</span>
            <Badge variant="outline">{servers.length}</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {servers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {discovering ? (
                <>
                  <Search className="mx-auto h-8 w-8 mb-2 animate-spin" />
                  <p>Scanning network for CouchDB servers...</p>
                </>
              ) : (
                <>
                  <Server className="mx-auto h-8 w-8 mb-2" />
                  <p>No servers discovered. Click "Discover Servers" to scan.</p>
                </>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              {servers.map((server, index) => (
                <Card key={`${server.ip}:${server.port}`} className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="font-medium">{server.ip}:{server.port}</div>
                      <div className="text-sm text-muted-foreground">
                        URL: {server.url}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {currentServer?.url === server.url && (
                        <Badge variant="default">Connected</Badge>
                      )}
                      <Button
                        onClick={() => connect(server)}
                        disabled={connected && currentServer?.url === server.url}
                        size="sm"
                        variant="outline"
                      >
                        {connected && currentServer?.url === server.url ? 'Connected' : 'Connect'}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Sync Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {connected ? (
              syncing ? (
                <RefreshCw className="h-5 w-5 animate-spin" />
              ) : (
                <Wifi className="h-5 w-5" />
              )
            ) : (
              <WifiOff className="h-5 w-5" />
            )}
            <span>Sync Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <div className="text-sm font-medium">Connection</div>
              <Badge variant={connected ? "default" : "secondary"}>
                {connected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Sync Active</div>
              <Badge variant={syncing ? "default" : "secondary"}>
                {syncing ? 'Syncing' : 'Idle'}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Last Sync</div>
              <div className="text-xs">
                <Clock className="inline mr-1 h-3 w-3" />
                {formatTime(status.lastSync)}
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm font-medium">Server</div>
              <div className="text-xs font-mono">
                {currentServer ? `${currentServer.ip}:${currentServer.port}` : 'None'}
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center text-sm font-medium">
                <Download className="mr-2 h-4 w-4" />
                Documents Received
              </div>
              <div className="text-2xl font-bold">{status.docsReceived}</div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center text-sm font-medium">
                <Upload className="mr-2 h-4 w-4" />
                Documents Sent
              </div>
              <div className="text-2xl font-bold">{status.docsSent}</div>
            </div>
          </div>

          {currentServer && (
            <>
              <Separator />
              <div className="space-y-2">
                <div className="text-sm font-medium">Remote Database URL</div>
                <div className="text-xs font-mono bg-gray-100 p-2 rounded break-all">
                  {currentServer.url}/{dbStatus.dbName || 'resto-[restaurant-id]'}
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Real-time Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
          <CardDescription>
            Technical details for troubleshooting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="text-xs font-mono bg-gray-50 p-3 rounded space-y-1">
            <div>🏠 Environment: {typeof window !== 'undefined' ? 'Browser' : 'Server'}</div>
            <div>📊 MainDB Initialized: {dbStatus.initialized ? '✅' : '❌'}</div>
            <div>🆔 Restaurant ID: {dbStatus.restaurantId || 'Not set'}</div>
            <div>💾 Database Name: {dbStatus.dbName || 'Not set'}</div>
            <div>🔍 Servers Found: {servers.length}</div>
            <div>🔗 Connected: {connected ? '✅' : '❌'}</div>
            <div>🔄 Syncing: {syncing ? '✅' : '❌'}</div>
            <div>📥 Docs Received: {status.docsReceived}</div>
            <div>📤 Docs Sent: {status.docsSent}</div>
            <div>⏰ Last Sync: {status.lastSync ? status.lastSync.toISOString() : 'Never'}</div>
            {error && <div>❌ Error: {error}</div>}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}