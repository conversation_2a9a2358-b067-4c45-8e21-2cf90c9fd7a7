/**
 * Simple Sync Test - Verify sync is working
 * 
 * This creates test documents to verify that sync is actually working
 * between mobile PouchDB and desktop CouchDB.
 */

import { simpleSync } from './simple-sync';

export interface SyncTestResult {
  success: boolean;
  message: string;
  details?: any;
  testDocId?: string;
  syncTime?: number;
}

export class SimpleSyncTest {
  
  /**
   * Complete mobile PouchDB flow test - from init to sync
   */
  static async testCompleteMobileFlow(): Promise<{
    success: boolean;
    details: any;
    recommendations: string[];
    flowSteps: string[];
  }> {
    const details: any = {};
    const recommendations: string[] = [];
    const flowSteps: string[] = [];
    
    try {
      flowSteps.push('🚀 Starting complete mobile PouchDB flow test');
      
      // Step 1: Environment Detection
      flowSteps.push('📱 Step 1: Environment Detection');
      const isMobile = typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor);
      
      const isElectron = typeof window !== 'undefined' && 
        (Boolean((window as any).IS_DESKTOP_APP) || 
         Boolean((window as any).electronAPI) || 
         Boolean((window as any).process?.versions?.electron));
      
      details.environment = {
        isMobile,
        isElectron,
        hasCapacitor: typeof window !== 'undefined' ? Boolean((window as any).Capacitor) : false,
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'N/A',
        hasIndexedDB: typeof window !== 'undefined' ? Boolean(window.indexedDB) : false
      };
      
      if (isElectron) {
        flowSteps.push('🖥️ Desktop environment detected - this test is for mobile');
        recommendations.push('This is a desktop environment - mobile flow test not applicable');
        return { success: true, details, recommendations, flowSteps };
      }
      
      if (!isMobile) {
        flowSteps.push('🌐 Browser environment detected - testing browser PouchDB flow');
      } else {
        flowSteps.push('📱 Mobile environment detected - testing mobile PouchDB flow');
      }
      
      // Step 2: Auth Data Check
      flowSteps.push('🔐 Step 2: Auth Data Verification');
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        flowSteps.push('❌ No auth data found');
        recommendations.push('CRITICAL: User not logged in - no auth data');
        return { success: false, details, recommendations, flowSteps };
      }
      
      const parsedAuth = JSON.parse(authData);
      const restaurantId = parsedAuth.restaurantId;
      if (!restaurantId) {
        flowSteps.push('❌ No restaurant ID in auth data');
        recommendations.push('CRITICAL: No restaurant ID in auth data');
        return { success: false, details, recommendations, flowSteps };
      }
      
      flowSteps.push(`✅ Restaurant ID found: ${restaurantId}`);
      details.auth = { restaurantId };
      
      // Step 3: PouchDB Library Loading Test
      flowSteps.push('📚 Step 3: PouchDB Library Loading');
      const { initPouchDB } = await import('@/lib/db/pouchdb-init');
      
      const startLibraryLoad = Date.now();
      const PouchDB = await initPouchDB();
      const libraryLoadTime = Date.now() - startLibraryLoad;
      
      if (!PouchDB) {
        flowSteps.push('❌ PouchDB library failed to load');
        recommendations.push('CRITICAL: PouchDB library not available');
        return { success: false, details, recommendations, flowSteps };
      }
      
      flowSteps.push(`✅ PouchDB library loaded in ${libraryLoadTime}ms`);
      details.libraryLoad = { success: true, loadTime: libraryLoadTime };
      
      // Step 4: Direct PouchDB Instance Test
      flowSteps.push('🗄️ Step 4: Direct PouchDB Instance Creation');
      const { getRestaurantDbName } = await import('@/lib/db/db-utils');
      const dbName = getRestaurantDbName(restaurantId);
      
      const testDB = new PouchDB(`${dbName}-flow-test`, { 
        auto_compaction: true,
        adapter: isMobile ? 'idb' : undefined // Use IndexedDB on mobile
      });
      
      // Test basic methods exist
      const hasSync = typeof testDB.sync === 'function';
      const hasPut = typeof testDB.put === 'function';
      const hasGet = typeof testDB.get === 'function';
      
      flowSteps.push(`✅ Direct PouchDB instance created: ${dbName}-flow-test`);
      flowSteps.push(`✅ Sync method available: ${hasSync}`);
      flowSteps.push(`✅ Put method available: ${hasPut}`);
      flowSteps.push(`✅ Get method available: ${hasGet}`);
      
      details.directPouchDB = { hasSync, hasPut, hasGet, dbName };
      
      // Step 5: Basic Operations Test
      flowSteps.push('🧪 Step 5: Basic Database Operations');
      const testDoc = { _id: 'flow-test-doc', test: true, timestamp: Date.now() };
      
      const putResult = await testDB.put(testDoc);
      flowSteps.push(`✅ Put operation successful: ${putResult.id}`);
      
      const getResult = await testDB.get('flow-test-doc');
      flowSteps.push(`✅ Get operation successful: ${getResult._id}`);
      
      await testDB.remove(getResult._id, getResult._rev);
      flowSteps.push('✅ Remove operation successful');
      
      // Clean up test database
      await testDB.destroy();
      flowSteps.push('✅ Test database cleaned up');
      
      details.basicOperations = { putSuccess: true, getSuccess: true, removeSuccess: true };
      
      // Step 6: Database Wrapper Flow Test
      flowSteps.push('🔄 Step 6: Database Wrapper Integration');
      const { getMainDbInstance } = await import('@/lib/db/pouchdb-instance');
      const dbWrapper = getMainDbInstance();
      
      // Force initialization
      await dbWrapper.initialize(restaurantId);
      flowSteps.push('✅ Database wrapper initialization called');
      
      // Test our retry logic
      let attempts = 0;
      const maxAttempts = 10;
      let realDB: any = null;
      const waitStartTime = Date.now();
      
      while (attempts < maxAttempts) {
        const db = dbWrapper.getDatabase();
        if (db && typeof db.put === 'function' && typeof db.sync === 'function') {
          realDB = db;
          break;
        }
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }
      
      const waitTime = Date.now() - waitStartTime;
      
      if (!realDB) {
        flowSteps.push(`❌ Real database not ready after ${waitTime}ms (${attempts} attempts)`);
        recommendations.push('CRITICAL: Database wrapper retry logic failed');
        return { success: false, details, recommendations, flowSteps };
      }
      
      flowSteps.push(`✅ Real database ready after ${waitTime}ms (${attempts} attempts)`);
      details.wrapperIntegration = { attempts, waitTime, success: true };
      
      // Step 7: Sync Capability Test
      flowSteps.push('🔄 Step 7: Sync Capability Verification');
      const syncTestDoc = { _id: 'sync-capability-test', test: true, timestamp: Date.now() };
      
      await realDB.put(syncTestDoc);
      const retrievedDoc = await realDB.get('sync-capability-test');
      await realDB.remove(retrievedDoc._id, retrievedDoc._rev);
      
      flowSteps.push('✅ Sync-ready database operations successful');
      
      // Test sync method (without actually syncing)
      const hasSyncMethod = typeof realDB.sync === 'function';
      flowSteps.push(`✅ Sync method available on real database: ${hasSyncMethod}`);
      
      details.syncCapability = { hasSyncMethod, operationsWork: true };
      
      // Step 8: Final Assessment
      flowSteps.push('🎯 Step 8: Final Assessment');
      const allTestsPassed = details.libraryLoad.success && 
                            details.directPouchDB.hasSync && 
                            details.basicOperations.putSuccess && 
                            details.wrapperIntegration.success && 
                            details.syncCapability.hasSyncMethod;
      
      if (allTestsPassed) {
        flowSteps.push('🎉 ALL TESTS PASSED - Mobile PouchDB flow is working correctly');
        recommendations.push('✅ Mobile PouchDB initialization and sync capability verified');
        recommendations.push('✅ Database wrapper retry logic working');
        recommendations.push('✅ Ready for sync operations');
      } else {
        flowSteps.push('❌ Some tests failed - check details above');
        recommendations.push('❌ Mobile PouchDB flow has issues');
      }
      
      return { 
        success: allTestsPassed, 
        details, 
        recommendations, 
        flowSteps 
      };
      
    } catch (error) {
      flowSteps.push(`❌ Flow test failed with error: ${error instanceof Error ? error.message : String(error)}`);
      details.error = error instanceof Error ? error.message : String(error);
      recommendations.push('CRITICAL: Mobile PouchDB flow test failed with error');
      return { success: false, details, recommendations, flowSteps };
    }
  }

  /**
   * Mobile-specific database diagnostic
   */
  static async diagnoseMobileDatabase(): Promise<{
    success: boolean;
    details: any;
    recommendations: string[];
  }> {
    const details: any = {};
    const recommendations: string[] = [];
    
    try {
      // 1. Environment detection
      const isMobile = typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor);
      
      details.environment = {
        isMobile,
        hasCapacitor: typeof window !== 'undefined' ? Boolean((window as any).Capacitor) : false,
        userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'N/A',
        hasIndexedDB: typeof window !== 'undefined' ? Boolean(window.indexedDB) : false,
        hasLocalStorage: typeof window !== 'undefined' ? Boolean(window.localStorage) : false
      };
      
      if (!isMobile) {
        recommendations.push('Not running on mobile - this diagnostic is for mobile issues');
      }
      
      // 2. Auth data check
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        details.auth = { hasAuthData: false };
        recommendations.push('CRITICAL: No auth data found - user not logged in');
        return { success: false, details, recommendations };
      }
      
      const parsedAuth = JSON.parse(authData);
      const restaurantId = parsedAuth.restaurantId;
      details.auth = {
        hasAuthData: true,
        hasRestaurantId: Boolean(restaurantId),
        restaurantId
      };
      
      if (!restaurantId) {
        recommendations.push('CRITICAL: No restaurant ID in auth data');
        return { success: false, details, recommendations };
      }
      
      // 3. Database wrapper check
      const { getMainDbInstance } = await import('@/lib/db/pouchdb-instance');
      const dbWrapper = getMainDbInstance();
      
      details.wrapper = {
        exists: Boolean(dbWrapper),
        isInitialized: dbWrapper?.isInitialized || false,
        currentRestaurantId: dbWrapper?.getCurrentRestaurantId() || null
      };
      
      // 4. Force initialization and wait for real database
      console.log('[MobileDiagnostic] 🔄 Testing database initialization...');
      await dbWrapper.initialize(restaurantId);
      
      // 5. Test waiting for real PouchDB instance
      let attempts = 0;
      const maxAttempts = 10;
      let realDB: any = null;
      const waitTimes: number[] = [];
      
      while (attempts < maxAttempts) {
        const startTime = Date.now();
        const db = dbWrapper.getDatabase();
        const waitTime = Date.now() - startTime;
        waitTimes.push(waitTime);
        
        if (db && typeof db.put === 'function' && typeof db.sync === 'function') {
          realDB = db;
          console.log(`[MobileDiagnostic] ✅ Real PouchDB ready after ${attempts * 500}ms`);
          break;
        }
        
        console.log(`[MobileDiagnostic] ⏳ Attempt ${attempts + 1}: DB not ready, waiting...`);
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }
      
      details.databaseWait = {
        attempts,
        maxAttempts,
        waitTimes,
        totalWaitTime: attempts * 500,
        success: Boolean(realDB)
      };
      
      if (!realDB) {
        recommendations.push('CRITICAL: Real PouchDB instance never became ready');
        return { success: false, details, recommendations };
      }
      
      // 6. Test PouchDB functionality
      details.pouchDBTest = {
        hasSync: typeof realDB.sync === 'function',
        hasPut: typeof realDB.put === 'function',
        hasGet: typeof realDB.get === 'function',
        hasRemove: typeof realDB.remove === 'function',
        constructor: realDB.constructor?.name || 'unknown'
      };
      
      // 7. Test basic database operations
      try {
        const testDoc = { _id: 'mobile-diagnostic-test', test: true, timestamp: Date.now() };
        const putResult = await realDB.put(testDoc);
        const getResult = await realDB.get('mobile-diagnostic-test');
        await realDB.remove(getResult._id, getResult._rev);
        
        details.operationTest = {
          putSuccess: Boolean(putResult.ok),
          getSuccess: Boolean(getResult._id),
          removeSuccess: true
        };
        
        console.log('[MobileDiagnostic] ✅ All database operations successful');
      } catch (opError) {
        details.operationTest = {
          error: opError instanceof Error ? opError.message : String(opError)
        };
        recommendations.push('Database operations failed - PouchDB not working properly');
      }
      
      const success = Boolean(realDB) && details.pouchDBTest.hasSync && details.pouchDBTest.hasPut;
      
      if (success) {
        recommendations.push('✅ Mobile database is working correctly');
      } else {
        recommendations.push('❌ Mobile database has issues - check details above');
      }
      
      return { success, details, recommendations };
      
    } catch (error) {
      details.error = error instanceof Error ? error.message : String(error);
      recommendations.push('Diagnostic failed with error - check console');
      return { success: false, details, recommendations };
    }
  }
  
  /**
   * Test sync by creating a test document and verifying it syncs
   */
  static async testSync(): Promise<SyncTestResult> {
    try {
      console.log('[SimpleSyncTest] Starting sync test...');
      
      // Check if simple sync is initialized
      const syncStatus = simpleSync.getSyncStatus();
      if (syncStatus.length === 0) {
        return {
          success: false,
          message: 'No active sync connections found'
        };
      }
      
      // CONSISTENT APPROACH: Use the same database pattern as SimpleSync and rest of app
      console.log('[SimpleSyncTest] 🔄 Using consistent database access pattern...');
      
      // Get restaurant ID from auth
      const authData = localStorage.getItem('auth_data');
      if (!authData) {
        return {
          success: false,
          message: 'No auth data found - user may not be logged in'
        };
      }
      
      const parsedAuth = JSON.parse(authData);
      const restaurantId = parsedAuth.restaurantId;
      if (!restaurantId) {
        return {
          success: false,
          message: 'No restaurant ID in auth data'
        };
      }
      
      // MOBILE FIX: Same approach as SimpleSync - wait for REAL database
      const { getMainDbInstance } = await import('@/lib/db/pouchdb-instance');
      const dbWrapper = getMainDbInstance();
      
      console.log('[SimpleSyncTest] 🔄 Ensuring REAL database is ready (same as SimpleSync)...');
      
      // Force proper initialization
      await dbWrapper.initialize(restaurantId);
      
      // Wait for the real PouchDB instance to be created (same logic as SimpleSync)
      let attempts = 0;
      const maxAttempts = 20; // 10 seconds max wait
      let localDB: any = null;
      
      while (attempts < maxAttempts) {
        const db = dbWrapper.getDatabase();
        if (db && typeof db.put === 'function' && typeof db.get === 'function') {
          console.log(`[SimpleSyncTest] ✅ Real PouchDB instance ready after ${attempts * 500}ms`);
          localDB = db;
          break;
        }
        
        console.log(`[SimpleSyncTest] ⏳ Waiting for real PouchDB instance... attempt ${attempts + 1}/${maxAttempts}`);
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }
      
      if (!localDB) {
        return {
          success: false,
          message: `Real PouchDB instance not ready after ${maxAttempts * 500}ms - database initialization failed`
        };
      }
      
      console.log('[SimpleSyncTest] ✅ Real database is ready with proper PouchDB instance');
      
      // Verify it's a proper PouchDB instance
      console.log('[SimpleSyncTest] 🔍 Verifying PouchDB instance...');
      console.log('[SimpleSyncTest] 🔍 Has put method:', typeof localDB.put);
      console.log('[SimpleSyncTest] 🔍 Has get method:', typeof localDB.get);
      
      if (typeof localDB.put !== 'function') {
        return {
          success: false,
          message: 'Database instance is missing put method - not a valid PouchDB instance'
        };
      }
      
      // Create a test document
      const testDocId = `sync-test-${Date.now()}`;
      const testDoc = {
        _id: testDocId,
        type: 'sync_test',
        message: 'This is a sync test document',
        timestamp: new Date().toISOString(),
        platform: typeof window !== 'undefined' && (window as any).Capacitor ? 'mobile' : 'web',
        testId: Math.random().toString(36).substr(2, 9)
      };
      
      console.log(`[SimpleSyncTest] Creating test document: ${testDocId}`);
      
      const startTime = Date.now();
      
      // Put the test document
      const putResult = await localDB.put(testDoc);
      console.log(`[SimpleSyncTest] Test document created:`, putResult);
      
      // Wait a moment for sync to occur
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Verify the document exists locally
      const retrievedDoc = await localDB.get(testDocId);
      console.log(`[SimpleSyncTest] Test document retrieved:`, retrievedDoc);
      
      const syncTime = Date.now() - startTime;
      
      // Clean up - remove test document
      try {
        await localDB.remove(retrievedDoc._id, retrievedDoc._rev);
        console.log(`[SimpleSyncTest] Test document cleaned up`);
      } catch (cleanupError) {
        console.warn(`[SimpleSyncTest] Failed to clean up test document:`, cleanupError);
      }
      
      return {
        success: true,
        message: `Sync test completed successfully in ${syncTime}ms`,
        details: {
          testDocId,
          syncConnections: syncStatus.length,
          activeConnections: syncStatus.filter(s => s.status === 'active').length
        },
        testDocId,
        syncTime
      };
      
    } catch (error) {
      console.error('[SimpleSyncTest] Sync test failed:', error);
      return {
        success: false,
        message: `Sync test failed: ${error instanceof Error ? error.message : String(error)}`,
        details: error
      };
    }
  }
  
  /**
   * Get sync health summary
   */
  static getSyncHealth(): {
    isHealthy: boolean;
    activeConnections: number;
    totalConnections: number;
    totalDocsTransferred: number;
    lastSyncTime?: Date;
    issues: string[];
  } {
    const syncStatus = simpleSync.getSyncStatus();
    const activeConnections = syncStatus.filter(s => s.status === 'active').length;
    const errorConnections = syncStatus.filter(s => s.status === 'error');
    const totalDocsTransferred = simpleSync.getTotalDocsTransferred();
    
    const lastSyncTimes = syncStatus
      .map(s => s.lastSync)
      .filter(Boolean)
      .sort((a, b) => (b?.getTime() || 0) - (a?.getTime() || 0));
    
    const issues: string[] = [];
    
    if (syncStatus.length === 0) {
      issues.push('No sync connections established');
    }
    
    if (activeConnections === 0 && syncStatus.length > 0) {
      issues.push('No active sync connections');
    }
    
    if (errorConnections.length > 0) {
      issues.push(`${errorConnections.length} sync connections have errors`);
    }
    
    return {
      isHealthy: activeConnections > 0 && errorConnections.length === 0,
      activeConnections,
      totalConnections: syncStatus.length,
      totalDocsTransferred,
      lastSyncTime: lastSyncTimes[0],
      issues
    };
  }
  
  /**
   * Get detailed sync status for debugging
   */
  static getDetailedStatus(): {
    connections: any[];
    hasActiveSyncs: boolean;
    totalDocsTransferred: number;
    summary: string;
  } {
    const connections = simpleSync.getSyncStatus();
    const hasActiveSyncs = simpleSync.hasActiveSyncs();
    const totalDocsTransferred = simpleSync.getTotalDocsTransferred();
    
    const activeCount = connections.filter(c => c.status === 'active').length;
    const errorCount = connections.filter(c => c.status === 'error').length;
    
    let summary = '';
    if (connections.length === 0) {
      summary = 'No sync connections';
    } else if (activeCount > 0) {
      summary = `${activeCount} active sync${activeCount > 1 ? 's' : ''}, ${totalDocsTransferred} docs transferred`;
    } else if (errorCount > 0) {
      summary = `${errorCount} sync error${errorCount > 1 ? 's' : ''}`;
    } else {
      summary = `${connections.length} sync connection${connections.length > 1 ? 's' : ''} (inactive)`;
    }
    
    return {
      connections,
      hasActiveSyncs,
      totalDocsTransferred,
      summary
    };
  }
}