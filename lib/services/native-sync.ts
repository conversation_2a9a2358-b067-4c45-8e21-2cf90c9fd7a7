import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';
import { getRestaurantDbName } from '@/lib/db/db-utils';

interface SyncServer {
  ip: string;
  port: number;
  url: string;
}

interface SyncStatus {
  connected: boolean;
  syncing: boolean;
  lastSync?: Date;
  error?: string;
  docsReceived: number;
  docsSent: number;
}

interface SyncOptions {
  live?: boolean;
  retry?: boolean;
  auth?: {
    username: string;
    password: string;
  };
}

class NativeSyncService {
  private syncHandler: any = null;
  private currentServer: SyncServer | null = null;
  private status: SyncStatus = {
    connected: false,
    syncing: false,
    docsReceived: 0,
    docsSent: 0
  };
  private listeners: ((status: SyncStatus) => void)[] = [];

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  onStatusChange(listener: (status: SyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  async startSync(server: SyncServer, options: SyncOptions = {}): Promise<boolean> {
    console.log(`🔄 Starting sync with ${server.url}...`);

    // Prevent syncing to self (localhost, 127.0.0.1, or local network IP)
    if (this.isSelfSync(server)) {
      console.warn(`⚠️ Skipping sync to self: ${server.url}`);
      this.status.error = 'Cannot sync to self';
      this.emitStatusUpdate();
      return false;
    }

    try {
      await this.waitForDatabase();
      
      const localDb = mainDbInstance.getPouchDBForSync();
      if (!localDb) {
        throw new Error('Local database not available for sync operations');
      }

      const restaurantId = mainDbInstance.getCurrentRestaurantId();
      if (!restaurantId) {
        throw new Error('Restaurant ID not available');
      }

      const dbName = getRestaurantDbName(restaurantId);
      const remoteUrl = `${server.url}/${dbName}`;
      
      console.log(`📡 Syncing ${dbName} with ${remoteUrl}`);

      const syncOptions = {
        live: options.live ?? true,
        retry: options.retry ?? true,
        ...options
      };

      this.currentServer = server;
      this.status = {
        connected: true,
        syncing: true,
        docsReceived: 0,
        docsSent: 0
      };
      this.emitStatusUpdate();

      this.syncHandler = localDb.sync(remoteUrl, syncOptions)
        .on('change', (info: any) => {
          console.log('📊 Sync change:', info);
          if (info.direction === 'pull') {
            this.status.docsReceived += info.change?.docs_read || 0;
          } else if (info.direction === 'push') {
            this.status.docsSent += info.change?.docs_written || 0;
          }
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('paused', () => {
          console.log('⏸️ Sync paused');
          this.status.syncing = false;
          this.emitStatusUpdate();
        })
        .on('active', () => {
          console.log('▶️ Sync active');
          this.status.syncing = true;
          this.emitStatusUpdate();
        })
        .on('denied', (err: any) => {
          console.error('❌ Sync denied:', err);
          this.status.error = `Access denied: ${err.message}`;
          this.emitStatusUpdate();
        })
        .on('complete', (info: any) => {
          console.log('✅ Sync complete:', info);
          this.status.syncing = false;
          this.status.lastSync = new Date();
          this.emitStatusUpdate();
        })
        .on('error', (err: any) => {
          console.error('❌ Sync error:', err);
          this.status.error = err.message;
          this.status.connected = false;
          this.status.syncing = false;
          this.emitStatusUpdate();
        });

      return true;
    } catch (error: any) {
      console.error('❌ Failed to start sync:', error);
      this.status.error = error.message;
      this.status.connected = false;
      this.status.syncing = false;
      this.emitStatusUpdate();
      return false;
    }
  }

  async stopSync(): Promise<void> {
    if (this.syncHandler) {
      console.log('🛑 Stopping sync...');
      this.syncHandler.cancel();
      this.syncHandler = null;
    }

    this.currentServer = null;
    this.status = {
      connected: false,
      syncing: false,
      docsReceived: this.status.docsReceived,
      docsSent: this.status.docsSent,
      lastSync: this.status.lastSync
    };
    this.emitStatusUpdate();
  }

  private isSelfSync(server: SyncServer): boolean {
    // Check for localhost and 127.0.0.1
    if (server.ip === 'localhost' || server.ip === '127.0.0.1') {
      return true;
    }

    // Get current machine's local IP (simplified check)
    if (typeof window !== 'undefined') {
      // In browser, we can't easily get local IP, but we can check common patterns
      const currentHost = window.location.hostname;
      if (currentHost === server.ip) {
        return true;
      }
    }

    // Check if this is an Electron app with its own CouchDB server
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      // In Electron, assume any local network IP could be self if running embedded CouchDB
      const localNetworkPattern = /^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|169\.254\.)/;
      if (localNetworkPattern.test(server.ip)) {
        // Additional check: if this is the default CouchDB port and we're in Electron
        if (server.port === 5984) {
          console.log(`🔍 Detected potential self-sync in Electron: ${server.ip}:${server.port}`);
          return true;
        }
      }
    }

    return false;
  }

  private async waitForDatabase(maxWaitMs = 10000): Promise<void> {
    const startTime = Date.now();
    
    while (!mainDbInstance.isInitialized) {
      if (Date.now() - startTime > maxWaitMs) {
        throw new Error('Database initialization timeout');
      }
      
      console.log('⏳ Waiting for database initialization...');
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('✅ Database ready for sync');
  }

  getStatus(): SyncStatus {
    return { ...this.status };
  }

  getCurrentServer(): SyncServer | null {
    return this.currentServer;
  }

  isConnected(): boolean {
    return this.status.connected;
  }

  isSyncing(): boolean {
    return this.status.syncing;
  }
}

export const nativeSyncService = new NativeSyncService();
export type { SyncServer, SyncStatus, SyncOptions };