// DEPRECATED: This file is no longer used.
// The mobile app now uses SimpleIPDiscovery through AutonomousSyncProvider.
// This stub file exists only to prevent import errors in deprecated code.

import { PeerInfo, SyncStatus } from '../../types/p2p-sync';

// Stub types for backward compatibility
export { PeerInfo, SyncStatus };

// Stub functions for backward compatibility
export function logZeroconfMessage(message: string): void {
  console.warn('[DEPRECATED] logZeroconfMessage:', message);
  console.warn('Use SimpleIPDiscovery through AutonomousSyncProvider instead');
}

export function onZeroconfLog(callback: (log: string) => void): void {
  console.warn('[DEPRECATED] onZeroconfLog: Use SimpleIPDiscovery through AutonomousSyncProvider instead');
  // Call the callback once with a deprecation message
  callback('DEPRECATED: Use SimpleIPDiscovery through AutonomousSyncProvider instead');
}

// Stub class for backward compatibility
export class ZeroConfDiscovery {
  constructor(deviceId: string) {
    console.error('[DEPRECATED] ZeroConfDiscovery: Use SimpleIPDiscovery through AutonomousSyncProvider instead');
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }

  async initialize(port: number): Promise<boolean> {
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }

  async startWatching(): Promise<boolean> {
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }

  onPeerDiscovered(callback: (peer: PeerInfo) => void): void {
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }

  onPeerLost(callback: (peerId: string) => void): void {
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }

  getPeers(): PeerInfo[] {
    throw new Error('ZeroConfDiscovery is deprecated. Use SimpleIPDiscovery through AutonomousSyncProvider instead.');
  }
}
