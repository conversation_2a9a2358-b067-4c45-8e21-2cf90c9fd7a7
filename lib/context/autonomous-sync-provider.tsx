'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { autonomousSyncService, type AutoSyncStatus } from '@/lib/services/autonomous-sync-service';

interface AutonomousSyncContextType {
  status: AutoSyncStatus;
  manualDiscovery: () => Promise<void>;
  enable: () => void;
  disable: () => void;
}

const AutonomousSyncContext = createContext<AutonomousSyncContextType | undefined>(undefined);

interface AutonomousSyncProviderProps {
  children: ReactNode;
}

export function AutonomousSyncProvider({ children }: AutonomousSyncProviderProps) {
  const [status, setStatus] = useState<AutoSyncStatus>(autonomousSyncService.getStatus());

  useEffect(() => {
    const unsubscribe = autonomousSyncService.onStatusChange(setStatus);
    
    const startService = async () => {
      try {
        await autonomousSyncService.start();
        console.log('🤖 Autonomous sync service started');
      } catch (error) {
        console.error('❌ Failed to start autonomous sync service:', error);
      }
    };

    startService();

    return () => {
      unsubscribe();
      autonomousSyncService.stop();
    };
  }, []);

  const manualDiscovery = async () => {
    await autonomousSyncService.manualDiscovery();
  };

  const enable = () => {
    autonomousSyncService.updateConfig({ enabled: true });
  };

  const disable = () => {
    autonomousSyncService.updateConfig({ enabled: false });
  };

  const contextValue: AutonomousSyncContextType = {
    status,
    manualDiscovery,
    enable,
    disable
  };

  return (
    <AutonomousSyncContext.Provider value={contextValue}>
      {children}
    </AutonomousSyncContext.Provider>
  );
}

export function useAutonomousSync(): AutonomousSyncContextType {
  const context = useContext(AutonomousSyncContext);
  if (context === undefined) {
    throw new Error('useAutonomousSync must be used within an AutonomousSyncProvider');
  }
  return context;
}