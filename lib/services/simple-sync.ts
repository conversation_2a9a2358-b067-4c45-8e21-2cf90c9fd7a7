// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

"use client";

import { EventEmitter } from 'events';
import { PeerInfo } from '@/types/p2p-sync';
import { SimpleIPDiscovery } from './simple-ip-discovery';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';
import { getRestaurantDbName } from '@/lib/db/db-utils';

export interface SimpleSyncConnection {
  id: string;
  peerId: string;
  dbName: string;
  remoteUrl: string;
  status: 'connecting' | 'active' | 'error';
  replication?: any;
  lastSync?: Date;
  docsTransferred: number;
}

export class SimpleSync extends EventEmitter {
  private discovery: SimpleIPDiscovery;
  private connections = new Map<string, SimpleSyncConnection>();
  private discoveryTimer?: NodeJS.Timeout;
  public isRunning = false;

  constructor() {
    super();
    this.discovery = new SimpleIPDiscovery();
  }

  async start(): Promise<void> {
    if (this.isRunning) return;
    
    console.log('🚀 Starting simple sync...');
    this.isRunning = true;
    
    // Initial discovery
    await this.discoverAndSync();
    
    // Continuous discovery every 30 seconds
    this.discoveryTimer = setInterval(() => {
      this.discoverAndSync().catch(console.error);
    }, 30000);
  }

  async stop(): Promise<void> {
    if (!this.isRunning) return;
    
    console.log('🛑 Stopping simple sync...');
    this.isRunning = false;
    
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
      this.discoveryTimer = undefined;
    }
    
    // Cancel all replications
    for (const conn of this.connections.values()) {
      if (conn.replication && typeof conn.replication.cancel === 'function') {
        conn.replication.cancel();
      }
    }
    this.connections.clear();
  }

  private async discoverAndSync(): Promise<void> {
    try {
      this.emit('discovery-started');
      console.log('🔍 [SimpleSync] Starting device discovery...');
      
      const result = await this.discovery.findDevices();
      
      const couchDBServers = result.devices.filter(d => 
        (d.serverInfo?.couchdb || d.port >= 5984 && d.port <= 5987) && !d.isSelf
      ).length;
      
      console.log(`📊 [SimpleSync] Discovery completed: ${result.devices.length} devices, ${couchDBServers} CouchDB servers`);
      
      this.emit('discovery-completed', {
        totalDevices: result.devices.length,
        couchDBServers,
        duration: result.scanDuration
      });
      
      for (const device of result.devices) {
        // Skip self devices
        if (device.isSelf) {
          console.log(`🚫 [SimpleSync] Skipping self device: ${device.ip}:${device.port}`);
          continue;
        }
        
        // Convert DiscoveredDevice to PeerInfo format
        const peer = {
          id: device.deviceId || `${device.ip}:${device.port}`,
          ip: device.ip,
          port: device.port,
          hostname: device.hostname
        };
        await this.syncWithServer(peer);
      }
    } catch (error) {
      console.error('❌ [SimpleSync] Discovery failed:', error);
      this.emit('discovery-error', { message: error instanceof Error ? error.message : String(error) });
    }
  }

  private async syncWithServer(peer: PeerInfo): Promise<void> {
    try {
      // Get restaurant ID and database name first
      const restaurantId = mainDbInstance.getCurrentRestaurantId();
      if (!restaurantId) {
        console.error('❌ [SimpleSync] No restaurant ID available for sync');
        this.emit('sync-error', { 
          connectionId: `${peer.ip}:${peer.port}`, 
          error: { message: 'No restaurant ID available' }
        });
        return;
      }

      const dbName = getRestaurantDbName(restaurantId);
      const connectionId = `${peer.ip}:${peer.port}-${dbName}`;
      
      // Skip if already connected
      if (this.connections.has(connectionId)) {
        console.log(`🔄 [SimpleSync] Already connected to ${connectionId}, skipping`);
        return;
      }

      // 🚀 FIX: Use getPouchDBForSync() for native PouchDB sync
      const localDb = mainDbInstance.getPouchDBForSync();
      if (!localDb) {
        console.error('❌ [SimpleSync] PouchDB not available for sync - mobile environment required');
        this.emit('sync-error', { 
          connectionId, 
          error: { message: 'PouchDB not available for sync - mobile environment required' }
        });
        return;
      }

      // Verify sync capabilities exist
      if (typeof localDb.sync !== 'function') {
        console.error('❌ [SimpleSync] PouchDB instance lacks sync capability');
        this.emit('sync-error', { 
          connectionId, 
          error: { message: 'PouchDB instance lacks sync capability' }
        });
        return;
      }

      const remoteUrl = `http://admin:admin@${peer.ip}:${peer.port}/${dbName}`;
      
      console.log(`🔄 [SimpleSync] Starting native PouchDB sync: ${connectionId} -> ${remoteUrl}`);

      const connection: SimpleSyncConnection = {
        id: connectionId,
        peerId: peer.id,
        dbName,
        remoteUrl,
        status: 'connecting',
        docsTransferred: 0
      };

      this.connections.set(connectionId, connection);

      // 🚀 Native PouchDB sync - dead simple pattern
      const replication = localDb.sync(remoteUrl, {
        live: true,
        retry: true,
        timeout: 30000,
        back_off_function: (delay: number) => Math.min(delay * 2, 30000)
      });

      connection.replication = replication;
      connection.status = 'active';
      connection.lastSync = new Date();

      // Enhanced event handling with better logging
      replication.on('change', (info: any) => {
        const docsWritten = info.docs_written || 0;
        connection.docsTransferred += docsWritten;
        connection.lastSync = new Date();
        
        if (docsWritten > 0) {
          console.log(`📄 [SimpleSync] Sync progress ${connectionId}: +${docsWritten} docs (total: ${connection.docsTransferred})`);
        }
        
        this.emit('sync-progress', { 
          connectionId, 
          docsTransferred: connection.docsTransferred,
          lastSync: connection.lastSync,
          changeInfo: info
        });
      });

      replication.on('error', (error: any) => {
        console.error(`❌ [SimpleSync] Sync error for ${connectionId}:`, error);
        connection.status = 'error';
        this.connections.delete(connectionId);
        
        this.emit('sync-error', { 
          connectionId, 
          error: {
            message: error.message || 'Unknown sync error',
            status: error.status || 500,
            name: error.name || 'sync_error'
          }
        });
      });

      replication.on('complete', (info: any) => {
        console.log(`✅ [SimpleSync] Sync completed for ${connectionId}`, info);
        this.emit('sync-complete', { connectionId, info });
      });

      replication.on('denied', (error: any) => {
        console.warn(`🚫 [SimpleSync] Sync denied for ${connectionId}:`, error);
        this.emit('sync-denied', { connectionId, error });
      });

      replication.on('paused', (error: any) => {
        console.log(`⏸️ [SimpleSync] Sync paused for ${connectionId}:`, error || 'No error');
        this.emit('sync-paused', { connectionId, error });
      });

      replication.on('active', () => {
        console.log(`▶️ [SimpleSync] Sync resumed for ${connectionId}`);
        this.emit('sync-active', { connectionId });
      });

      console.log(`✅ [SimpleSync] Native PouchDB sync started: ${connectionId}`);
      this.emit('sync-started', { connectionId, remoteUrl });

    } catch (error) {
      console.error(`❌ [SimpleSync] Failed to sync with ${peer.ip}:${peer.port}:`, error);
      this.emit('sync-error', { 
        connectionId: `${peer.ip}:${peer.port}`, 
        error: {
          message: error instanceof Error ? error.message : String(error),
          type: 'sync_setup_error'
        }
      });
    }
  }

  getConnections(): SimpleSyncConnection[] {
    return Array.from(this.connections.values());
  }

  getStats() {
    const connections = this.getConnections();
    return {
      totalConnections: connections.length,
      activeConnections: connections.filter(c => c.status === 'active').length,
      errorConnections: connections.filter(c => c.status === 'error').length,
      totalDocsTransferred: connections.reduce((sum, c) => sum + c.docsTransferred, 0)
    };
  }

  getDiscoveryState() {
    return {
      isScanning: this.discovery.getIsScanning(),
      stats: this.discovery.getStats(),
      diagnostics: this.discovery.getNetworkDiagnostics(),
      cachedDevices: this.discovery.getCachedDevices()
    };
  }

  async manualDiscovery(): Promise<any> {
    console.log('🔄 [SimpleSync] Manual discovery triggered');
    return await this.discoverAndSync();
  }
}

// Export singleton instance
export const simpleSync = new SimpleSync();