"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { mobilePouchDBTest, type MobilePouchDBTestResult } from '@/lib/services/mobile-pouchdb-test';
import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';
import { initPouchDB } from '@/lib/db/pouchdb-init';

/**
 * 🔧 Mobile PouchDB Debug Interface
 * 
 * Comprehensive testing and debugging interface for mobile PouchDB initialization
 */
export function MobilePouchDBDebug() {
  const [testResults, setTestResults] = useState<MobilePouchDBTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [environmentInfo, setEnvironmentInfo] = useState<any>(null);

  /**
   * Run complete mobile PouchDB test suite
   */
  const runCompleteTest = async () => {
    setIsRunning(true);
    try {
      console.log('🧪 Starting Mobile PouchDB Complete Test...');
      const results = await mobilePouchDBTest.runCompleteTest();
      setTestResults(results);
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * Get current environment information
   */
  const getEnvironmentInfo = () => {
    const info = {
      // Environment detection
      isMobile: typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor),
      hasCapacitor: typeof window !== 'undefined' && !!(window as any).Capacitor,
      hasIndexedDB: typeof window !== 'undefined' && !!window.indexedDB,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      
      // PouchDB status
      hasPouchDBGlobal: typeof window !== 'undefined' && !!(window as any).PouchDB,
      
      // Main DB instance status
      mainDbInitialized: mainDbInstance.isInitialized,
      mainDbHasDatabase: !!mainDbInstance.getDatabase(),
      
      // Browser capabilities
      hasLocalStorage: typeof localStorage !== 'undefined',
      hasSessionStorage: typeof sessionStorage !== 'undefined',
      isOnline: typeof navigator !== 'undefined' ? navigator.onLine : false
    };
    
    setEnvironmentInfo(info);
    console.log('🔍 Environment Info:', info);
  };

  /**
   * Test PouchDB library loading only
   */
  const testPouchDBLoading = async () => {
    setIsRunning(true);
    try {
      console.log('🧪 Testing PouchDB library loading...');
      
      // Clear existing PouchDB
      if ((window as any).PouchDB) {
        delete (window as any).PouchDB;
      }
      
      const PouchDB = await initPouchDB();
      
      if (PouchDB) {
        console.log('✅ PouchDB loaded successfully');
        console.log('PouchDB details:', {
          isFunction: typeof PouchDB === 'function',
          hasPlugin: typeof PouchDB.plugin === 'function',
          hasSync: typeof PouchDB.prototype.sync === 'function',
          hasReplicate: typeof PouchDB.prototype.replicate === 'object'
        });
      } else {
        console.error('❌ PouchDB loading failed');
      }
    } catch (error) {
      console.error('❌ PouchDB loading error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * Test main DB instance initialization
   */
  const testMainDbInit = async () => {
    setIsRunning(true);
    try {
      console.log('🧪 Testing main DB instance initialization...');
      
      // Reset main DB instance
      mainDbInstance.isInitialized = false;
      
      const restaurantId = 'restaurant:debug_test_' + Date.now().toString(36);
      await mainDbInstance.initialize(restaurantId);
      
      const db = mainDbInstance.getDatabase();
      
      console.log('Main DB Instance Status:', {
        initialized: mainDbInstance.isInitialized,
        hasDatabase: !!db,
        restaurantId: restaurantId
      });
      
      if (db) {
        // Test basic operation
        const testDoc = { _id: 'debug_test', test: true, timestamp: Date.now() };
        await db.put(testDoc);
        const retrieved = await db.get('debug_test');
        await db.remove(retrieved._id, retrieved._rev);
        console.log('✅ Basic database operations successful');
      }
      
    } catch (error) {
      console.error('❌ Main DB initialization error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * Clear all data and reset
   */
  const clearAndReset = () => {
    setTestResults([]);
    setEnvironmentInfo(null);
    
    // Clear global PouchDB
    if ((window as any).PouchDB) {
      delete (window as any).PouchDB;
    }
    
    // Reset main DB instance
    mainDbInstance.isInitialized = false;
    
    console.log('🧹 Cleared all data and reset state');
  };

  const passedTests = testResults.filter(r => r.success).length;
  const failedTests = testResults.filter(r => !r.success).length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📱 Mobile PouchDB Debug Interface
            <Badge variant="outline">v2.0</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button 
              onClick={getEnvironmentInfo}
              variant="outline"
              size="sm"
            >
              🔍 Environment
            </Button>
            
            <Button 
              onClick={testPouchDBLoading}
              variant="outline"
              size="sm"
              disabled={isRunning}
            >
              📚 Test Loading
            </Button>
            
            <Button 
              onClick={testMainDbInit}
              variant="outline"
              size="sm"
              disabled={isRunning}
            >
              🗄️ Test Main DB
            </Button>
            
            <Button 
              onClick={clearAndReset}
              variant="outline"
              size="sm"
            >
              🧹 Clear & Reset
            </Button>
          </div>
          
          <Separator />
          
          <div className="flex gap-2">
            <Button 
              onClick={runCompleteTest}
              disabled={isRunning}
              className="flex-1"
            >
              {isRunning ? '🔄 Running Tests...' : '🧪 Run Complete Test Suite'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Environment Information */}
      {environmentInfo && (
        <Card>
          <CardHeader>
            <CardTitle>🔍 Environment Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Mobile Detection:</strong>
                <div className="ml-2">
                  <div>Is Mobile: <Badge variant={environmentInfo.isMobile ? "default" : "secondary"}>{environmentInfo.isMobile ? "Yes" : "No"}</Badge></div>
                  <div>Has Capacitor: <Badge variant={environmentInfo.hasCapacitor ? "default" : "secondary"}>{environmentInfo.hasCapacitor ? "Yes" : "No"}</Badge></div>
                  <div>Has IndexedDB: <Badge variant={environmentInfo.hasIndexedDB ? "default" : "secondary"}>{environmentInfo.hasIndexedDB ? "Yes" : "No"}</Badge></div>
                </div>
              </div>
              
              <div>
                <strong>Database Status:</strong>
                <div className="ml-2">
                  <div>PouchDB Global: <Badge variant={environmentInfo.hasPouchDBGlobal ? "default" : "secondary"}>{environmentInfo.hasPouchDBGlobal ? "Loaded" : "Not Loaded"}</Badge></div>
                  <div>Main DB Init: <Badge variant={environmentInfo.mainDbInitialized ? "default" : "secondary"}>{environmentInfo.mainDbInitialized ? "Yes" : "No"}</Badge></div>
                  <div>Has Database: <Badge variant={environmentInfo.mainDbHasDatabase ? "default" : "secondary"}>{environmentInfo.mainDbHasDatabase ? "Yes" : "No"}</Badge></div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 text-xs text-muted-foreground">
              <strong>User Agent:</strong> {environmentInfo.userAgent}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              🧪 Test Results
              <div className="flex gap-2">
                <Badge variant="default">✅ {passedTests}</Badge>
                <Badge variant="destructive">❌ {failedTests}</Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{result.step}</span>
                    <Badge variant={result.success ? "default" : "destructive"}>
                      {result.success ? "✅ Pass" : "❌ Fail"}
                    </Badge>
                  </div>
                  
                  {result.error && (
                    <div className="text-sm text-red-600 mb-2">
                      <strong>Error:</strong> {result.error}
                    </div>
                  )}
                  
                  {result.details && (
                    <div className="text-xs text-muted-foreground">
                      <strong>Details:</strong>
                      <pre className="mt-1 bg-muted p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(result.details, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
