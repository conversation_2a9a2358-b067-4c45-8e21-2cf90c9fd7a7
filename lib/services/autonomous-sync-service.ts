import { mainDbInstance } from '@/lib/db/v4/core/db-main-instance';
import { discoverCouchDBServers } from './ip-discovery';
import { nativeSyncService, type SyncServer } from './native-sync';

interface AutoSyncConfig {
  enabled: boolean;
  discoveryInterval: number;
  maxRetries: number;
  retryDelay: number;
  autoConnect: boolean;
}

interface AutoSyncStatus {
  enabled: boolean;
  discovering: boolean;
  connected: boolean;
  syncing: boolean;
  lastDiscovery?: Date;
  serversFound: number;
  currentServer?: SyncServer;
  error?: string;
}

class AutonomousSyncService {
  private config: AutoSyncConfig = {
    enabled: true,
    discoveryInterval: 30000,
    maxRetries: 3,
    retryDelay: 5000,
    autoConnect: true
  };

  private status: AutoSyncStatus = {
    enabled: true,
    discovering: false,
    connected: false,
    syncing: false,
    serversFound: 0
  };

  private discoveryTimer: NodeJS.Timeout | null = null;
  private retryTimer: NodeJS.Timeout | null = null;
  private retryCount = 0;
  private listeners: ((status: AutoSyncStatus) => void)[] = [];
  private networkListeners = false;

  private emitStatusUpdate(): void {
    this.listeners.forEach(listener => listener({ ...this.status }));
  }

  onStatusChange(listener: (status: AutoSyncStatus) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) this.listeners.splice(index, 1);
    };
  }

  async start(): Promise<void> {
    if (!this.config.enabled) return;

    console.log('🤖 Starting autonomous sync service...');
    
    this.setupNetworkListeners();
    this.setupSyncStatusListener();
    
    await this.checkAndStartDiscovery();
    this.schedulePeriodicDiscovery();
  }

  stop(): void {
    console.log('🛑 Stopping autonomous sync service...');
    
    this.clearTimers();
    this.removeNetworkListeners();
    
    this.status.enabled = false;
    this.emitStatusUpdate();
  }

  private async checkAndStartDiscovery(): Promise<void> {
    if (!this.shouldStartDiscovery()) return;

    await this.performDiscovery();
  }

  private shouldStartDiscovery(): boolean {
    return (
      this.config.enabled &&
      !this.status.discovering &&
      !this.status.connected &&
      this.isDatabaseReady() &&
      this.isNetworkOnline()
    );
  }

  private isDatabaseReady(): boolean {
    return mainDbInstance.isInitialized && !!mainDbInstance.getCurrentRestaurantId();
  }

  private isNetworkOnline(): boolean {
    return typeof navigator !== 'undefined' ? navigator.onLine : true;
  }

  private isSelfSync(server: SyncServer): boolean {
    // Check for localhost and 127.0.0.1
    if (server.ip === 'localhost' || server.ip === '127.0.0.1') {
      return true;
    }

    // Get current machine's local IP (simplified check)
    if (typeof window !== 'undefined') {
      // In browser, we can't easily get local IP, but we can check common patterns
      const currentHost = window.location.hostname;
      if (currentHost === server.ip) {
        return true;
      }
    }

    // Check if this is an Electron app with its own CouchDB server
    if (typeof window !== 'undefined' && (window as any).electronAPI) {
      // In Electron, assume any local network IP could be self if running embedded CouchDB
      const localNetworkPattern = /^(192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|169\.254\.)/;
      if (localNetworkPattern.test(server.ip)) {
        // Additional check: if this is the default CouchDB port and we're in Electron
        if (server.port === 5984) {
          console.log(`🔍 Autonomous sync detected potential self-sync: ${server.ip}:${server.port}`);
          return true;
        }
      }
    }

    return false;
  }

  private async performDiscovery(): Promise<void> {
    if (this.status.discovering) return;

    this.status.discovering = true;
    this.status.error = undefined;
    this.retryCount = 0;
    this.emitStatusUpdate();

    try {
      console.log('🔍 Auto-discovering CouchDB servers...');
      
      const servers = await discoverCouchDBServers({
        timeout: 3000,
        maxConcurrent: 10
      });

      this.status.serversFound = servers.length;
      this.status.lastDiscovery = new Date();

      if (servers.length > 0) {
        console.log(`✅ Auto-discovered ${servers.length} server(s)`);
        
        if (this.config.autoConnect && !this.status.connected) {
          // Find first non-self server
          const validServer = servers.find(server => !this.isSelfSync(server));
          
          if (validServer) {
            await this.attemptConnection(validServer);
          } else {
            console.log('⚠️ All discovered servers are self-sync, skipping connection');
            this.status.error = 'All servers are self-sync';
            this.emitStatusUpdate();
          }
        }
      } else {
        console.log('⚠️ No servers discovered, will retry later');
        this.scheduleRetry();
      }

    } catch (error: any) {
      console.error('❌ Auto-discovery failed:', error);
      this.status.error = error.message;
      this.scheduleRetry();
    } finally {
      this.status.discovering = false;
      this.emitStatusUpdate();
    }
  }

  private async attemptConnection(server: SyncServer): Promise<void> {
    try {
      console.log(`🔗 Auto-connecting to ${server.url}...`);
      
      const success = await nativeSyncService.startSync(server, {
        live: true,
        retry: true
      });

      if (success) {
        console.log(`✅ Auto-connected to ${server.url}`);
        this.status.currentServer = server;
        this.retryCount = 0;
        this.scheduleConnectionHealthMonitoring();
      } else {
        throw new Error('Connection failed');
      }

    } catch (error: any) {
      console.error('❌ Auto-connection failed:', error);
      this.status.error = error.message;
      this.scheduleRetry();
    }
  }

  private scheduleConnectionHealthMonitoring(): void {
    const healthCheckTimer = setInterval(() => {
      if (!this.status.connected) {
        console.log('🏥 Connection health check failed, clearing timer');
        clearInterval(healthCheckTimer);
        return;
      }

      if (!this.isNetworkOnline()) {
        console.log('📵 Network offline during health check');
        return;
      }

      const syncStatus = nativeSyncService.getStatus();
      if (syncStatus.error && this.status.connected) {
        console.log('🚨 Health check detected sync errors, triggering recovery');
        clearInterval(healthCheckTimer);
        this.scheduleHealthCheck();
      }
    }, 15000);
  }

  private scheduleRetry(): void {
    if (this.retryCount >= this.config.maxRetries) {
      console.log('⏹️ Max retries reached, stopping auto-discovery');
      return;
    }

    this.retryCount++;
    const delay = this.config.retryDelay * this.retryCount;
    
    console.log(`⏰ Scheduling retry ${this.retryCount}/${this.config.maxRetries} in ${delay}ms`);
    
    this.retryTimer = setTimeout(() => {
      this.checkAndStartDiscovery();
    }, delay);
  }

  private schedulePeriodicDiscovery(): void {
    if (this.discoveryTimer) return;

    this.discoveryTimer = setInterval(() => {
      if (!this.status.connected) {
        this.checkAndStartDiscovery();
      }
    }, this.config.discoveryInterval);
  }

  private setupNetworkListeners(): void {
    if (this.networkListeners || typeof window === 'undefined') return;

    const handleOnline = () => {
      console.log('🌐 Network came online, restarting sync discovery');
      this.status.error = undefined;
      this.retryCount = 0;
      setTimeout(() => this.checkAndStartDiscovery(), 1000);
    };

    const handleOffline = () => {
      console.log('📵 Network went offline, pausing auto-sync');
      this.status.error = 'Network offline';
      this.clearTimers();
      this.emitStatusUpdate();
    };

    const handleVisibilityChange = () => {
      if (!document.hidden && this.isNetworkOnline() && !this.status.connected) {
        console.log('👁️ App became visible, checking sync status');
        setTimeout(() => this.checkAndStartDiscovery(), 500);
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    this.networkListeners = true;
  }

  private removeNetworkListeners(): void {
    if (!this.networkListeners || typeof window === 'undefined') return;

    window.removeEventListener('online', this.checkAndStartDiscovery);
    window.removeEventListener('offline', () => {});
    document.removeEventListener('visibilitychange', () => {});
    
    this.networkListeners = false;
  }

  private setupSyncStatusListener(): void {
    nativeSyncService.onStatusChange((syncStatus) => {
      const wasConnected = this.status.connected;
      
      this.status.connected = syncStatus.connected;
      this.status.syncing = syncStatus.syncing;
      
      if (syncStatus.error) {
        console.log('🔄 Sync error detected:', syncStatus.error);
        this.status.error = syncStatus.error;
        
        if (wasConnected && !syncStatus.connected) {
          console.log('🔄 Connection lost, scheduling health check and recovery');
          this.status.currentServer = undefined;
          this.scheduleHealthCheck();
        }
      } else if (syncStatus.connected && !wasConnected) {
        console.log('✅ Sync connection established');
        this.status.error = undefined;
        this.retryCount = 0;
      }
      
      this.emitStatusUpdate();
    });
  }

  private scheduleHealthCheck(): void {
    setTimeout(async () => {
      if (!this.status.connected && this.isNetworkOnline() && this.isDatabaseReady()) {
        console.log('🏥 Running health check and attempting recovery');
        await this.performDiscovery();
      }
    }, 5000);
  }

  private clearTimers(): void {
    if (this.discoveryTimer) {
      clearInterval(this.discoveryTimer);
      this.discoveryTimer = null;
    }
    
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }
  }

  getStatus(): AutoSyncStatus {
    return { ...this.status };
  }

  updateConfig(newConfig: Partial<AutoSyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.enabled === false) {
      this.stop();
    } else if (newConfig.enabled === true && !this.status.enabled) {
      this.start();
    }
  }

  async manualDiscovery(): Promise<void> {
    this.retryCount = 0;
    await this.performDiscovery();
  }
}

export const autonomousSyncService = new AutonomousSyncService();
export type { AutoSyncConfig, AutoSyncStatus };