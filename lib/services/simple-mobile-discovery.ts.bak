"use client";

// DEPRECATED: This file will be renamed to .bak
// All existing sync systems are being deprecated and replaced with a simpler implementation
// DO NOT USE THIS FILE - IT WILL BE REMOVED

import { CapacitorHttp } from '@capacitor/core';
import { isMobileApp } from '@/lib/utils/environment';

export interface MobileDiscoveredServer {
  ip: string;
  port: number;
  responseTime: number;
  serverInfo?: any;
  isCouchDB: boolean;
  lastSeen: Date;
}

export interface MobileDiscoveryResult {
  servers: MobileDiscoveredServer[];
  scanDuration: number;
  errors: string[];
}

/**
 * 🚀 Simple Mobile Discovery - Reliable CouchDB Server Discovery
 * 
 * Focused, simple implementation for mobile environments
 * No overcomplexity, just find CouchDB servers and connect
 */
export class SimpleMobileDiscovery {
  private readonly COMMON_IPS = [
    '***********', '***********', '***********', '***********', '***********',
    '***********0', '***********1', '***********2', '***********3', '***********4',
    '***********00', '***********01', '***********02', '***********03', '***********04',
    '***********', '***********', '***********', '***********', '***********',
    '********', '********', '********', '********', '********'
  ];
  
  private readonly COUCHDB_PORTS = [5984, 5985, 5986, 5987];
  private readonly TIMEOUT = 1000; // 1 second timeout
  
  private isScanning = false;
  private foundServers: MobileDiscoveredServer[] = [];
  
  /**
   * Quick scan for CouchDB servers
   * Tests common IPs first, then expands if needed
   */
  async quickScan(): Promise<MobileDiscoveryResult> {
    if (this.isScanning) {
      return {
        servers: [...this.foundServers],
        scanDuration: 0,
        errors: ['Already scanning']
      };
    }
    
    const startTime = Date.now();
    const errors: string[] = [];
    const servers: MobileDiscoveredServer[] = [];
    
    this.isScanning = true;
    
    try {
      console.log('🔍 [SimpleMobileDiscovery] Starting quick CouchDB scan...');
      
      // Phase 1: Test common IPs with CouchDB ports
      for (const ip of this.COMMON_IPS) {
        for (const port of this.COUCHDB_PORTS) {
          try {
            const server = await this.testServer(ip, port);
            if (server) {
              servers.push(server);
              console.log(`✅ Found CouchDB server: ${ip}:${port}`);
              
              // If we found a CouchDB server, we can stop scanning
              if (server.isCouchDB) {
                console.log('🎯 Found CouchDB server, stopping scan');
                break;
              }
            }
          } catch (error) {
            // Silently continue - expected for most IPs
          }
        }
        
        // Early exit if we found CouchDB servers
        if (servers.some(s => s.isCouchDB)) {
          break;
        }
      }
      
      // Phase 2: If no CouchDB found, do a broader scan of 192.168.1.x
      if (!servers.some(s => s.isCouchDB)) {
        console.log('🔍 No CouchDB in common IPs, scanning 192.168.1.x...');
        
        for (let i = 1; i <= 50; i++) { // Scan first 50 IPs
          const ip = `192.168.1.${i}`;
          
          // Skip if already tested
          if (this.COMMON_IPS.includes(ip)) continue;
          
          for (const port of this.COUCHDB_PORTS) {
            try {
              const server = await this.testServer(ip, port);
              if (server) {
                servers.push(server);
                console.log(`✅ Found server: ${ip}:${port}`);
                
                if (server.isCouchDB) {
                  console.log('🎯 Found CouchDB server, stopping scan');
                  break;
                }
              }
            } catch (error) {
              // Continue silently
            }
          }
          
          // Early exit if we found CouchDB
          if (servers.some(s => s.isCouchDB)) {
            break;
          }
        }
      }
      
      this.foundServers = servers;
      
      console.log(`🔍 [SimpleMobileDiscovery] Scan complete: ${servers.length} servers found`);
      
      return {
        servers,
        scanDuration: Date.now() - startTime,
        errors
      };
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      errors.push(errorMsg);
      console.error('❌ [SimpleMobileDiscovery] Scan failed:', errorMsg);
      
      return {
        servers,
        scanDuration: Date.now() - startTime,
        errors
      };
    } finally {
      this.isScanning = false;
    }
  }
  
  /**
   * Test a specific server
   */
  private async testServer(ip: string, port: number): Promise<MobileDiscoveredServer | null> {
    const startTime = Date.now();
    
    try {
      const url = `http://${ip}:${port}/`;
      
      const response = isMobileApp() 
        ? await CapacitorHttp.get({ 
            url, 
            connectTimeout: this.TIMEOUT,
            readTimeout: this.TIMEOUT
          })
        : await fetch(url, { 
            signal: AbortSignal.timeout(this.TIMEOUT),
            mode: 'no-cors' // Try no-cors for mobile browsers
          });
      
      const responseTime = Date.now() - startTime;
      
      // Check if response is successful
      const isSuccess = isMobileApp() 
        ? response.status === 200
        : response.ok || response.type === 'opaque'; // opaque means no-cors succeeded
      
      if (isSuccess) {
        let serverInfo: any = null;
        let isCouchDB = false;
        
        try {
          // Try to get server info
          if (isMobileApp() && response.data) {
            serverInfo = response.data;
            isCouchDB = !!(serverInfo?.couchdb || serverInfo?.version);
          } else if (!isMobileApp() && response.ok) {
            serverInfo = await response.json();
            isCouchDB = !!(serverInfo?.couchdb || serverInfo?.version);
          } else {
            // For no-cors, we can't read the response, but assume it might be CouchDB if it's on a CouchDB port
            isCouchDB = this.COUCHDB_PORTS.includes(port);
          }
        } catch (error) {
          // If we can't parse the response, but got a successful connection on a CouchDB port, assume it's CouchDB
          isCouchDB = this.COUCHDB_PORTS.includes(port);
        }
        
        return {
          ip,
          port,
          responseTime,
          serverInfo,
          isCouchDB,
          lastSeen: new Date()
        };
      }
      
      return null;
    } catch (error) {
      // Expected for most IPs - don't log
      return null;
    }
  }
  
  /**
   * Test a specific IP and port
   */
  async testSpecificServer(ip: string, port: number = 5984): Promise<MobileDiscoveredServer | null> {
    console.log(`🎯 [SimpleMobileDiscovery] Testing specific server: ${ip}:${port}`);
    return await this.testServer(ip, port);
  }
  
  /**
   * Get cached servers
   */
  getCachedServers(): MobileDiscoveredServer[] {
    return [...this.foundServers];
  }
  
  /**
   * Clear cache
   */
  clearCache(): void {
    this.foundServers = [];
    console.log('🧹 [SimpleMobileDiscovery] Cache cleared');
  }
  
  /**
   * Check if currently scanning
   */
  getIsScanning(): boolean {
    return this.isScanning;
  }
}

// Export singleton
export const simpleMobileDiscovery = new SimpleMobileDiscovery();