'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Wifi, 
  WifiOff, 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Activity,
  Server,
  Zap,
  RefreshCw
} from 'lucide-react';
import { useAutonomousSync } from '@/lib/hooks/use-autonomous-sync';

interface SyncStatusMonitorProps {
  className?: string;
}

export function MobileSyncStatusMonitor({ className }: SyncStatusMonitorProps) {
  const {
    isInitialized,
    isAutonomousActive,
    autonomousState,
    robustSyncStatus,
    healthSummary,
    serverInfo,
    logs,
    statusMessage
  } = useAutonomousSync(null);

  const [selectedTab, setSelectedTab] = useState('overview');
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh every 5 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Trigger a refresh by calling the hook again
      // This is handled automatically by the hook's internal state updates
    }, 5000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      case 'paused': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatTimestamp = (date: Date | string | undefined) => {
    if (!date) return 'Never';
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleTimeString();
  };

  const formatDuration = (start: Date | string | undefined) => {
    if (!start) return 'N/A';
    const startTime = typeof start === 'string' ? new Date(start) : start;
    const duration = Date.now() - startTime.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with overall status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              {isAutonomousActive ? (
                <Activity className="h-5 w-5 text-green-500" />
              ) : (
                <WifiOff className="h-5 w-5 text-gray-500" />
              )}
              Sync Status
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
              >
                <RefreshCw className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />
              </Button>
              <Badge variant={isAutonomousActive ? 'default' : 'secondary'}>
                {autonomousState.phase}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Status:</span>
              <p className="font-medium">{statusMessage}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Active Since:</span>
              <p className="font-medium">
                {formatDuration(autonomousState.connectionEstablishedTime)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for detailed information */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="databases">Databases</TabsTrigger>
          <TabsTrigger value="servers">Servers</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Health Summary */}
          {healthSummary && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Health Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      healthSummary.overall === 'healthy' ? 'bg-green-500' :
                      healthSummary.overall === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                    <span className={getHealthColor(healthSummary.overall)}>
                      {healthSummary.overall.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Active Alerts:</span>
                    <span className="ml-2 font-medium">
                      {healthSummary.activeAlerts?.length || 0}
                    </span>
                  </div>
                </div>

                {healthSummary.metrics && (
                  <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Sync Efficiency:</span>
                      <div className="mt-1">
                        <Progress value={healthSummary.metrics.syncEfficiency} className="h-2" />
                        <span className="text-xs text-muted-foreground">
                          {healthSummary.metrics.syncEfficiency.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Docs Transferred:</span>
                      <p className="font-medium">{healthSummary.metrics.totalDocsTransferred}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Active Alerts */}
          {healthSummary?.activeAlerts && healthSummary.activeAlerts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  Active Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-32">
                  <div className="space-y-2">
                    {healthSummary.activeAlerts.map((alert: any) => (
                      <div key={alert.id} className="flex items-start gap-2 p-2 bg-muted rounded">
                        {alert.type === 'error' ? (
                          <XCircle className="h-4 w-4 text-red-500 mt-0.5" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />
                        )}
                        <div className="flex-1 text-sm">
                          <p className="font-medium">{alert.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatTimestamp(alert.timestamp)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="databases" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Database className="h-4 w-4" />
                Database Sync Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {robustSyncStatus && robustSyncStatus.length > 0 ? (
                    robustSyncStatus.map((db: any) => (
                      <div key={db.dbName} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{db.dbName}</h4>
                          <Badge variant={db.isActive ? 'default' : 'secondary'}>
                            {db.status}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                          <div>Direction: {db.direction}</div>
                          <div>Docs: {db.docsTransferred || 0}</div>
                          <div>Conflicts: {db.conflicts || 0}</div>
                          <div>
                            Last Sync: {formatTimestamp(db.lastSync)}
                          </div>
                        </div>

                        {db.activeServer && (
                          <div className="mt-2 text-xs text-muted-foreground">
                            Server: {db.activeServer.id}
                          </div>
                        )}

                        {db.error && (
                          <div className="mt-2 text-xs text-red-600 bg-red-50 p-1 rounded">
                            {db.error}
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      No database sync information available
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="servers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Server className="h-4 w-4" />
                Server Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {serverInfo && serverInfo.length > 0 ? (
                    serverInfo.map((server: any) => (
                      <div key={server.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{server.id}</h4>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${
                              server.isHealthy ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                            <Badge variant={server.isHealthy ? 'default' : 'destructive'}>
                              {server.isHealthy ? 'Healthy' : 'Unhealthy'}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-sm text-muted-foreground">
                          <div>Response: {server.responseTime}ms</div>
                          <div>Errors: {server.errorCount}</div>
                          <div>Priority: {server.priority}</div>
                          <div>
                            Last Check: {formatTimestamp(server.lastHealthCheck)}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      No servers discovered
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Sync Logs
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-1 font-mono text-xs">
                  {logs && logs.length > 0 ? (
                    logs.map((log, index) => (
                      <div key={index} className="p-1 hover:bg-muted rounded">
                        {log}
                      </div>
                    ))
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      No logs available
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
