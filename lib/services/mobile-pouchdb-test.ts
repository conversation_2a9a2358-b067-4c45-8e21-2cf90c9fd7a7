"use client";

import { mainDbInstance } from '../db/v4/core/db-main-instance';
import { initPouchDB } from '../db/pouchdb-init';

/**
 * 🔧 Mobile PouchDB Comprehensive Test Suite
 * 
 * Tests the complete mobile PouchDB initialization flow from scratch
 * to verify everything works properly before sync attempts
 */

export interface MobilePouchDBTestResult {
  step: string;
  success: boolean;
  error?: string;
  details?: any;
}

export class MobilePouchDBTest {
  private results: MobilePouchDBTestResult[] = [];
  
  /**
   * Run complete mobile PouchDB test suite
   */
  async runCompleteTest(): Promise<MobilePouchDBTestResult[]> {
    this.results = [];
    
    console.log('🧪 Starting Mobile PouchDB Complete Test Suite...');
    
    // Step 1: Environment Detection
    await this.testEnvironmentDetection();
    
    // Step 2: PouchDB Library Loading
    await this.testPouchDBLibraryLoading();
    
    // Step 3: Database Instance Creation
    await this.testDatabaseInstanceCreation();
    
    // Step 4: Basic Database Operations
    await this.testBasicDatabaseOperations();
    
    // Step 5: Main DB Instance Integration
    await this.testMainDbInstanceIntegration();
    
    // Step 6: Sync Capabilities Verification
    await this.testSyncCapabilities();
    
    // Step 7: Complete Flow Test
    await this.testCompleteFlow();
    
    console.log('🧪 Mobile PouchDB Test Suite Complete');
    this.logResults();
    
    return this.results;
  }
  
  private addResult(step: string, success: boolean, error?: string, details?: any) {
    this.results.push({ step, success, error, details });
    
    if (success) {
      console.log(`✅ ${step}`);
    } else {
      console.error(`❌ ${step}: ${error}`);
    }
  }
  
  /**
   * Test 1: Environment Detection
   */
  private async testEnvironmentDetection() {
    try {
      const isMobile = typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor);
      
      const hasCapacitor = typeof window !== 'undefined' && !!(window as any).Capacitor;
      const hasIndexedDB = typeof window !== 'undefined' && !!window.indexedDB;
      
      const details = {
        isMobile,
        hasCapacitor,
        hasIndexedDB,
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown'
      };
      
      this.addResult('Environment Detection', true, undefined, details);
      
    } catch (error) {
      this.addResult('Environment Detection', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 2: PouchDB Library Loading
   */
  private async testPouchDBLibraryLoading() {
    try {
      console.log('🧪 Testing PouchDB library loading...');
      
      // Clear any existing PouchDB
      if ((window as any).PouchDB) {
        delete (window as any).PouchDB;
      }
      
      const PouchDB = await initPouchDB();
      
      if (!PouchDB) {
        throw new Error('initPouchDB returned null');
      }
      
      if (typeof PouchDB !== 'function') {
        throw new Error('PouchDB is not a constructor function');
      }
      
      const details = {
        hasConstructor: typeof PouchDB === 'function',
        hasPlugin: typeof PouchDB.plugin === 'function',
        hasSync: typeof PouchDB.prototype.sync === 'function',
        hasReplicate: typeof PouchDB.prototype.replicate === 'object'
      };
      
      this.addResult('PouchDB Library Loading', true, undefined, details);
      
    } catch (error) {
      this.addResult('PouchDB Library Loading', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 3: Database Instance Creation
   */
  private async testDatabaseInstanceCreation() {
    try {
      console.log('🧪 Testing database instance creation...');
      
      const PouchDB = (window as any).PouchDB;
      if (!PouchDB) {
        throw new Error('PouchDB not available from previous test');
      }
      
      const testDbName = `mobile_test_${Date.now()}`;
      const testDb = new PouchDB(testDbName, {
        adapter: 'idb',
        auto_compaction: true
      });
      
      if (!testDb) {
        throw new Error('Failed to create PouchDB instance');
      }
      
      // Test basic info
      const info = await testDb.info();
      
      // Clean up
      await testDb.destroy();
      
      const details = {
        dbName: testDbName,
        adapter: info.adapter,
        docCount: info.doc_count,
        updateSeq: info.update_seq
      };
      
      this.addResult('Database Instance Creation', true, undefined, details);
      
    } catch (error) {
      this.addResult('Database Instance Creation', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 4: Basic Database Operations
   */
  private async testBasicDatabaseOperations() {
    try {
      console.log('🧪 Testing basic database operations...');
      
      const PouchDB = (window as any).PouchDB;
      if (!PouchDB) {
        throw new Error('PouchDB not available');
      }
      
      const testDbName = `mobile_ops_test_${Date.now()}`;
      const testDb = new PouchDB(testDbName, {
        adapter: 'idb',
        auto_compaction: true
      });
      
      // Test PUT
      const testDoc = {
        _id: 'test_doc',
        name: 'Mobile Test Document',
        timestamp: Date.now(),
        mobile: true
      };
      
      const putResult = await testDb.put(testDoc);
      
      // Test GET
      const getResult = await testDb.get('test_doc');
      
      // Test UPDATE
      getResult.updated = true;
      const updateResult = await testDb.put(getResult);
      
      // Test DELETE
      const deleteResult = await testDb.remove(updateResult.id, updateResult.rev);
      
      // Clean up
      await testDb.destroy();
      
      const details = {
        put: putResult.ok,
        get: getResult._id === 'test_doc',
        update: updateResult.ok,
        delete: deleteResult.ok
      };
      
      this.addResult('Basic Database Operations', true, undefined, details);
      
    } catch (error) {
      this.addResult('Basic Database Operations', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 5: Main DB Instance Integration
   */
  private async testMainDbInstanceIntegration() {
    try {
      console.log('🧪 Testing main DB instance integration...');
      
      // Force re-initialization
      mainDbInstance.isInitialized = false;
      
      // Test initialization
      const restaurantId = 'restaurant:mobile_test_' + Date.now().toString(36);
      await mainDbInstance.initialize(restaurantId);
      
      if (!mainDbInstance.isInitialized) {
        throw new Error('Main DB instance not initialized');
      }
      
      const db = mainDbInstance.getDatabase();
      if (!db) {
        throw new Error('Main DB instance database is null');
      }
      
      // Test basic operation through main instance
      const testDoc = {
        _id: 'main_instance_test',
        test: true,
        timestamp: Date.now()
      };
      
      await db.put(testDoc);
      const retrieved = await db.get('main_instance_test');
      await db.remove(retrieved._id, retrieved._rev);
      
      const details = {
        initialized: mainDbInstance.isInitialized,
        hasDatabase: !!db,
        restaurantId: restaurantId
      };
      
      this.addResult('Main DB Instance Integration', true, undefined, details);
      
    } catch (error) {
      this.addResult('Main DB Instance Integration', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 6: Sync Capabilities Verification
   */
  private async testSyncCapabilities() {
    try {
      console.log('🧪 Testing sync capabilities...');
      
      const db = mainDbInstance.getDatabase();
      if (!db) {
        throw new Error('Main DB instance not available');
      }
      
      const hasSync = typeof db.sync === 'function';
      const hasReplicate = typeof db.replicate === 'object';
      const hasReplicateTo = hasReplicate && typeof db.replicate.to === 'function';
      const hasReplicateFrom = hasReplicate && typeof db.replicate.from === 'function';
      
      if (!hasSync) {
        throw new Error('PouchDB sync method not available');
      }
      
      if (!hasReplicate) {
        throw new Error('PouchDB replicate object not available');
      }
      
      const details = {
        hasSync,
        hasReplicate,
        hasReplicateTo,
        hasReplicateFrom
      };
      
      this.addResult('Sync Capabilities Verification', true, undefined, details);
      
    } catch (error) {
      this.addResult('Sync Capabilities Verification', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  /**
   * Test 7: Complete Flow Test
   */
  private async testCompleteFlow() {
    try {
      console.log('🧪 Testing complete mobile PouchDB flow...');
      
      // This simulates the complete flow from app startup to sync readiness
      
      // 1. Environment check
      const isMobile = typeof window !== 'undefined' && 
        (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
         !!(window as any).Capacitor);
      
      if (!isMobile) {
        console.warn('⚠️ Not in mobile environment, but continuing test...');
      }
      
      // 2. Fresh initialization
      const freshDbInstance = mainDbInstance;
      const restaurantId = 'restaurant:complete_flow_test_' + Date.now().toString(36);
      
      // 3. Initialize
      await freshDbInstance.initialize(restaurantId);
      
      // 4. Verify ready for sync
      const db = freshDbInstance.getDatabase();
      if (!db || typeof db.sync !== 'function') {
        throw new Error('Database not ready for sync');
      }
      
      // 5. Test sync preparation (without actual sync)
      const syncOptions = {
        live: false,
        retry: false
      };
      
      // This would be the actual sync call:
      // const syncHandler = db.sync('http://test-server:5984/test-db', syncOptions);
      
      const details = {
        isMobile,
        initialized: freshDbInstance.isInitialized,
        hasDatabase: !!db,
        hasSyncMethod: typeof db.sync === 'function',
        restaurantId
      };
      
      this.addResult('Complete Flow Test', true, undefined, details);
      
    } catch (error) {
      this.addResult('Complete Flow Test', false, error instanceof Error ? error.message : String(error));
    }
  }
  
  private logResults() {
    console.log('\n📊 Mobile PouchDB Test Results Summary:');
    console.log('=====================================');
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📊 Total: ${this.results.length}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.step}: ${result.error}`);
      });
    }
    
    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      console.log(`${result.success ? '✅' : '❌'} ${result.step}`);
      if (result.details) {
        console.log('  Details:', result.details);
      }
    });
  }
}

// Export singleton instance
export const mobilePouchDBTest = new MobilePouchDBTest();
