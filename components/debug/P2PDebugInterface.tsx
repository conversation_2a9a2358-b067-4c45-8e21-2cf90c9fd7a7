'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Wifi, 
  Server, 
  Smartphone, 
  Monitor,
  CheckCircle,
  XCircle,
  Play,
  Square,
  Search,
  Database,
  Network,
  Activity,
  AlertCircle,
  RefreshCw,
  Zap
} from 'lucide-react';
import { isMobileApp, isElectronApp, getPlatformName } from '@/lib/utils/environment';
import { useUnifiedDB } from '@/lib/context/unified-db-provider';
import { MobilePouchDBDebug } from './MobilePouchDBDebug';

interface P2PDebugInterfaceProps {
  className?: string;
}

export function P2PDebugInterface({ className }: P2PDebugInterfaceProps) {
  const [platform, setPlatform] = useState('unknown');
  const [isMobile, setIsMobile] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);
  const [isTestingSync, setIsTestingSync] = useState(false);
  const [localPouchDBStatus, setLocalPouchDBStatus] = useState<{
    available: boolean;
    hasSync: boolean;
    error?: string;
    dbName?: string;
  }>({ available: false, hasSync: false });

  // Get database instance and sync data
  const { db: mainDbInstance, isReady } = useUnifiedDB();
  const [isActive, setIsActive] = useState(false);
  const [connections, setConnections] = useState([]);
  const [stats, setStats] = useState({ activeConnections: 0, totalDocsTransferred: 0, errorConnections: 0 });
  const [discovery, setDiscovery] = useState({ 
    devicesFound: 0, 
    couchDBServers: 0, 
    isScanning: false, 
    lastScan: null, 
    discoveredServers: [], 
    errors: [] 
  });
  const [errors, setErrors] = useState([]);
  const [initStatus, setInitStatus] = useState('pending');

  useEffect(() => {
    const mobile = isMobileApp();
    const desktop = isElectronApp();
    setIsMobile(mobile);
    setIsDesktop(desktop);
    setPlatform(getPlatformName());
  }, []);

  // Check local PouchDB status
  useEffect(() => {
    const checkLocalPouchDB = async () => {
      if (!mainDbInstance || !isReady) {
        setLocalPouchDBStatus({ available: false, hasSync: false, error: 'Database not ready' });
        return;
      }

      try {
        const pouchDB = mainDbInstance.getPouchDBForSync();
        const restaurantId = mainDbInstance.getCurrentRestaurantId();
        
        setLocalPouchDBStatus({
          available: !!pouchDB,
          hasSync: pouchDB ? typeof pouchDB.sync === 'function' : false,
          dbName: restaurantId ? `resto-${restaurantId}` : undefined,
          error: !pouchDB ? 'PouchDB not available for sync' : undefined
        });
      } catch (error) {
        setLocalPouchDBStatus({
          available: false,
          hasSync: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    checkLocalPouchDB();
  }, [mainDbInstance, isReady, initStatus]);

  // Test sync functionality
  const handleTestSync = async () => {
    setIsTestingSync(true);
    setTestResult(null);
    
    try {
      if (!isActive) {
        setIsActive(true);
        setTestResult({
          success: true,
          message: 'Sync started successfully'
        });
      } else {
        setTestResult({
          success: true,
          message: 'Sync already active'
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `Test failed: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsTestingSync(false);
    }
  };

  // 🚀 FIX: Separate discovery trigger
  const handleTriggerDiscovery = async () => {
    setIsTestingSync(true);
    setTestResult(null);
    
    try {
      // Get the SimpleSync instance and trigger manual discovery
      const simpleSyncInstance = (window as any).__simpleSyncRef?.current;
      if (simpleSyncInstance && typeof simpleSyncInstance.manualDiscovery === 'function') {
        console.log('🔍 [P2PDebugInterface] Triggering manual discovery...');
        await simpleSyncInstance.manualDiscovery();
        setTestResult({
          success: true,
          message: 'Discovery scan triggered successfully'
        });
      } else {
        setTestResult({
          success: false,
          message: 'SimpleSync instance not available for manual discovery'
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `Discovery failed: ${error instanceof Error ? error.message : String(error)}`
      });
    } finally {
      setIsTestingSync(false);
    }
  };

  const handleStopSync = async () => {
    try {
      setIsActive(false);
    } catch (error) {
      console.error('Failed to stop sync:', error);
    }
  };

  return (
    <div className={className}>
      <Tabs defaultValue="discovery" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="discovery">🔍 Discovery</TabsTrigger>
          <TabsTrigger value="pouchdb">📱 PouchDB</TabsTrigger>
          <TabsTrigger value="couchdb">🗄️ CouchDB</TabsTrigger>
          <TabsTrigger value="sync">🔄 Sync</TabsTrigger>
          <TabsTrigger value="errors">⚠️ Errors</TabsTrigger>
        </TabsList>

        {/* 🔍 IP Discovery Tab */}
        <TabsContent value="discovery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Network className="h-5 w-5" />
                Network Discovery
              </CardTitle>
              <CardDescription>
                Scanning local network for CouchDB servers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Discovery Status */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{discovery.devicesFound}</div>
                  <div className="text-sm text-muted-foreground">Devices Found</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{discovery.couchDBServers}</div>
                  <div className="text-sm text-muted-foreground">CouchDB Servers</div>
                </div>
                <div className="text-center">
                  <Badge variant={discovery.isScanning ? "default" : "secondary"} className="text-xs">
                    {discovery.isScanning ? "Scanning..." : "Idle"}
                  </Badge>
                  <div className="text-sm text-muted-foreground mt-1">Status</div>
                </div>
              </div>

              {/* Last Scan Info */}
              {discovery.lastScan && (
                <div className="text-sm text-muted-foreground text-center">
                  Last scan: {discovery.lastScan.toLocaleTimeString()}
                </div>
              )}

              {/* Manual Discovery Button */}
              <Button 
                onClick={handleTriggerDiscovery} 
                disabled={isTestingSync || discovery.isScanning}
                size="sm"
                variant="outline"
                className="w-full"
              >
                <Search className="h-4 w-4 mr-2" />
                {discovery.isScanning ? 'Scanning Network...' : 'Scan Network Now'}
              </Button>

              {/* Discovered Servers */}
              {discovery.discoveredServers && discovery.discoveredServers.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium flex items-center gap-2">
                    <Server className="h-4 w-4" />
                    Discovered Servers ({discovery.discoveredServers.length})
                  </h4>
                  <div className="max-h-40 overflow-y-auto space-y-2">
                    {discovery.discoveredServers.map((server, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-mono text-sm font-medium">{server.ip}:{server.port}</span>
                          <div className="flex items-center gap-1">
                            {server.serverInfo?.couchdb && (
                              <Badge variant="default" className="text-xs">CouchDB</Badge>
                            )}
                            {server.isSelf && (
                              <Badge variant="secondary" className="text-xs">Self</Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              {server.responseTime}ms
                            </Badge>
                          </div>
                        </div>
                        {server.serverInfo?.version && (
                          <div className="text-xs text-muted-foreground">
                            Version: {server.serverInfo.version}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground">
                          Last seen: {server.lastSeen.toLocaleTimeString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Discovery Errors */}
              {discovery.errors.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium text-red-600 flex items-center gap-2">
                    <AlertCircle className="h-4 w-4" />
                    Discovery Issues ({discovery.errors.length})
                  </h4>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {discovery.errors.slice(-3).map((error, index) => (
                      <div key={index} className="text-xs p-2 bg-red-50 border-red-200 border rounded">
                        {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 📱 Local PouchDB Tab */}
        <TabsContent value="pouchdb" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Local PouchDB Status
              </CardTitle>
              <CardDescription>
                Local database instance for {isMobile ? 'mobile' : 'desktop'} sync
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Platform Info */}
              <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="space-y-1">
                  <div className="text-sm font-medium">Platform</div>
                  <div className="text-sm text-muted-foreground">{platform}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium">Environment</div>
                  <Badge variant={isMobile ? 'default' : isDesktop ? 'secondary' : 'outline'}>
                    {isMobile ? 'Mobile' : isDesktop ? 'Desktop' : 'Browser'}
                  </Badge>
                </div>
              </div>

              {/* PouchDB Status */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="font-medium">PouchDB Available</span>
                  <Badge variant={localPouchDBStatus.available ? 'default' : 'destructive'}>
                    {localPouchDBStatus.available ? (
                      <><CheckCircle className="h-3 w-3 mr-1" />Available</>
                    ) : (
                      <><XCircle className="h-3 w-3 mr-1" />Not Available</>
                    )}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium">Sync Capability</span>
                  <Badge variant={localPouchDBStatus.hasSync ? 'default' : 'destructive'}>
                    {localPouchDBStatus.hasSync ? (
                      <><CheckCircle className="h-3 w-3 mr-1" />Has Sync</>
                    ) : (
                      <><XCircle className="h-3 w-3 mr-1" />No Sync</>
                    )}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium">Database Name</span>
                  <span className="font-mono text-sm">
                    {localPouchDBStatus.dbName || 'Not Set'}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="font-medium">Initialization</span>
                  <Badge variant={
                    initStatus === 'ready' ? 'default' : 
                    initStatus === 'failed' ? 'destructive' : 
                    'secondary'
                  }>
                    {initStatus === 'ready' && <CheckCircle className="h-3 w-3 mr-1" />}
                    {initStatus === 'failed' && <XCircle className="h-3 w-3 mr-1" />}
                    {initStatus === 'pending' && <RefreshCw className="h-3 w-3 mr-1 animate-spin" />}
                    {initStatus.charAt(0).toUpperCase() + initStatus.slice(1)}
                  </Badge>
                </div>
              </div>

              {/* Error Display */}
              {localPouchDBStatus.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 text-red-800 font-medium mb-1">
                    <AlertCircle className="h-4 w-4" />
                    PouchDB Error
                  </div>
                  <div className="text-red-700 text-sm">{localPouchDBStatus.error}</div>
                </div>
              )}

              {/* Success Message */}
              {localPouchDBStatus.available && localPouchDBStatus.hasSync && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800 font-medium">
                    <CheckCircle className="h-4 w-4" />
                    PouchDB Ready for Sync
                  </div>
                  <div className="text-green-700 text-sm mt-1">
                    Local database is properly initialized and can sync with CouchDB servers
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 🗄️ CouchDB Servers Tab */}
        <TabsContent value="couchdb" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                CouchDB Server Status
              </CardTitle>
              <CardDescription>
                Remote CouchDB servers discovered and their reachability
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {discovery.discoveredServers && discovery.discoveredServers.length > 0 ? (
                <div className="space-y-3">
                  {discovery.discoveredServers
                    .filter(server => server.serverInfo?.couchdb && !server.isSelf)
                    .map((server, index) => (
                      <div key={index} className="p-4 border rounded-lg space-y-3">
                        {/* Server Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Server className="h-5 w-5 text-blue-600" />
                            <span className="font-mono font-medium">{server.ip}:{server.port}</span>
                          </div>
                          <Badge variant="default">CouchDB</Badge>
                        </div>

                        {/* Server Details */}
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Response Time:</span>
                            <div className="font-medium">{server.responseTime}ms</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Last Seen:</span>
                            <div className="font-medium">{server.lastSeen.toLocaleTimeString()}</div>
                          </div>
                          {server.serverInfo?.version && (
                            <div>
                              <span className="text-muted-foreground">Version:</span>
                              <div className="font-medium">{server.serverInfo.version}</div>
                            </div>
                          )}
                          <div>
                            <span className="text-muted-foreground">Status:</span>
                            <div className="flex items-center gap-1">
                              <CheckCircle className="h-3 w-3 text-green-600" />
                              <span className="text-green-600 font-medium">Reachable</span>
                            </div>
                          </div>
                        </div>

                        {/* Connection Test */}
                        <div className="pt-2 border-t">
                          <div className="text-sm text-muted-foreground mb-2">
                            Target Database: <span className="font-mono">{localPouchDBStatus.dbName}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            URL: http://admin:admin@{server.ip}:{server.port}/{localPouchDBStatus.dbName}
                          </div>
                        </div>
                      </div>
                    ))} 
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Server className="h-8 w-8 mx-auto mb-3 opacity-50" />
                  <div className="font-medium">No CouchDB Servers Found</div>
                  <div className="text-sm">Run network discovery to find CouchDB servers</div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 🔄 Native Sync Tab */}
        <TabsContent value="sync" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Native PouchDB ↔ CouchDB Sync
              </CardTitle>
              <CardDescription>
                Real-time synchronization between local PouchDB and remote CouchDB
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Sync Overview */}
              <div className="grid grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.activeConnections}</div>
                  <div className="text-sm text-muted-foreground">Active Syncs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stats.totalDocsTransferred}</div>
                  <div className="text-sm text-muted-foreground">Docs Synced</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.errorConnections}</div>
                  <div className="text-sm text-muted-foreground">Failed Syncs</div>
                </div>
              </div>

              {/* Sync Controls */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Badge variant={isActive ? "default" : "secondary"}>
                    {isActive ? (
                      <><Zap className="h-3 w-3 mr-1" />Sync Active</>
                    ) : (
                      <><Square className="h-3 w-3 mr-1" />Sync Stopped</>
                    )}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {isActive ? 'Continuously syncing with discovered servers' : 'Sync is not running'}
                  </span>
                </div>
                <div className="flex gap-2">
                  {!isActive ? (
                    <Button 
                      onClick={handleTestSync} 
                      disabled={isTestingSync || initStatus !== 'ready'}
                      size="sm"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {isTestingSync ? 'Starting...' : 'Start Sync'}
                    </Button>
                  ) : (
                    <Button 
                      onClick={handleStopSync}
                      variant="outline"
                      size="sm"
                    >
                      <Square className="h-4 w-4 mr-2" />
                      Stop Sync
                    </Button>
                  )}
                </div>
              </div>

              {/* Active Sync Connections */}
              {connections.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <Network className="h-4 w-4" />
                    Active Sync Connections ({connections.length})
                  </h4>
                  {connections.map((conn, index) => (
                    <div key={index} className="p-3 border rounded-lg space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            conn.status === 'active' ? 'bg-green-500' : 
                            conn.status === 'error' ? 'bg-red-500' : 
                            'bg-yellow-500'
                          }`} />
                          <span className="font-mono text-sm">{conn.remoteUrl}</span>
                        </div>
                        <Badge variant={
                          conn.status === 'active' ? 'default' : 
                          conn.status === 'error' ? 'destructive' : 
                          'secondary'
                        }>
                          {conn.status}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Documents:</span>
                          <div className="font-medium">{conn.docsTransferred}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Database:</span>
                          <div className="font-medium font-mono">{conn.dbName}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Last Sync:</span>
                          <div className="font-medium">
                            {conn.lastSync ? conn.lastSync.toLocaleTimeString() : 'Never'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* No Active Connections */}
              {connections.length === 0 && isActive && (
                <div className="text-center py-6 text-muted-foreground">
                  <Activity className="h-8 w-8 mx-auto mb-3 opacity-50" />
                  <div className="font-medium">No Active Sync Connections</div>
                  <div className="text-sm">Waiting for CouchDB servers to be discovered...</div>
                </div>
              )}

              {/* Test Results */}
              {testResult && (
                <div className={`p-3 rounded-lg border ${
                  testResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center gap-2">
                    {testResult.success ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="text-sm font-medium">
                      {testResult.message}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* ⚠️ Errors Tab */}
        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Error Log & Troubleshooting
              </CardTitle>
              <CardDescription>
                Detailed error information and troubleshooting guides
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {errors.length > 0 ? (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Recent Errors ({errors.length})</span>
                    <Badge variant="destructive">{errors.length}</Badge>
                  </div>
                  
                  {errors.slice(-10).reverse().map((error, index) => (
                    <div key={index} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant="destructive" className="text-xs">
                          {error.type.replace('_', ' ').toUpperCase()}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {error.timestamp.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="text-red-800 font-medium mb-2">
                        {error.message}
                      </div>
                      
                      {/* Troubleshooting Hints */}
                      <div className="text-sm text-red-700 bg-red-100 p-2 rounded">
                        {error.type === 'sync_error' && (
                          <div>
                            <strong>Troubleshooting:</strong> Check network connectivity to CouchDB servers. 
                            Ensure CouchDB is running and accessible on the network.
                          </div>
                        )}
                        {error.type === 'database_not_ready' && (
                          <div>
                            <strong>Troubleshooting:</strong> PouchDB initialization failed. 
                            Check if the database is properly configured and restaurant ID is set.
                          </div>
                        )}
                        {error.type === 'discovery_failed' && (
                          <div>
                            <strong>Troubleshooting:</strong> Network discovery failed. 
                            Check network permissions and ensure devices are on the same subnet.
                          </div>
                        )}
                        {error.type === 'no_restaurant_id' && (
                          <div>
                            <strong>Troubleshooting:</strong> No restaurant ID configured. 
                            Complete the restaurant setup or login process.
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-8 w-8 mx-auto mb-3 text-green-500" />
                  <div className="font-medium text-green-600">No Errors Found</div>
                  <div className="text-sm">All systems are operating normally</div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}